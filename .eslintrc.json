{"env": {"browser": true, "node": true, "jest": true}, "extends": ["eslint:recommended", "airbnb", "plugin:storybook/recommended"], "parser": "@babel/eslint-parser", "parserOptions": {"ecmaVersion": 6, "sourceType": "module", "ecmaFeatures": {"jsx": true}, "requireConfigFile": false}, "plugins": ["react"], "globals": {"window": true, "document": true, "localStorage": true, "FormData": true, "FileReader": true, "Blob": true, "API_URL": "readonly", "navigator": true}, "rules": {"default-param-last": 0, "import/extensions": 0, "import/no-named-as-default": 0, "linebreak-style": 0, "jsx-a11y/label-has-for": "off", "jsx-a11y/label-has-associated-control": "off", "class-methods-use-this": ["error", {"exceptMethods": ["render", "getInitialState", "getDefaultProps", "getChildContext", "componentWillMount", "componentDidMount", "componentWillReceiveProps", "shouldComponentUpdate", "componentWillUpdate", "componentDidUpdate", "componentWillUnmount", "componentDidCatch"]}], "react/function-component-definition": [1, {"namedComponents": "arrow-function", "unnamedComponents": "function-expression"}], "react/no-unstable-nested-components": 0, "react/jsx-filename-extension": [1, {"extensions": [".js", ".jsx", ".ts", ".tsx"]}], "react/jsx-uses-react": "error", "react/jsx-uses-vars": "error", "no-mixed-operators": 1, "no-unused-vars": ["error", {"varsIgnorePattern": "React"}], "no-underscore-dangle": 0, "import/imports-first": ["error", "absolute-first"], "import/newline-after-import": "error", "import/no-unresolved": "off", "import/no-extraneous-dependencies": ["error", {"devDependencies": true}], "react/jsx-props-no-spreading": "off"}, "overrides": [{"files": ["**/*.ts", "**/*.tsx"], "parserOptions": {"project": "tsconfig.json", "impliedStrict": true, "createDefaultProgram": false}, "rules": {"no-undef": "off", "react/require-default-props": "off"}}]}