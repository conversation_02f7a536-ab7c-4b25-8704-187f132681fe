variables:
  DOCKER_HOST: tcp://docker:2375
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  # Images
  CONTAINER_TEST_IMAGE: "$OKE_REGISTRY${OKE_PREFIX}$CI_PROJECT_PATH:$CI_COMMIT_SHORT_SHA"
  CONTAINER_LATEST_IMAGE: $OKE_REGISTRY${OKE_PREFIX}$CI_PROJECT_PATH:latest

stages:
  - lib
  - release
  - build
  - latest-image
  - deploy

.common-rules:
  rules:
    - if: $CI_COMMIT_TAG
      when: never
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_COMMIT_TAG == null
      when: on_success
    - if: $CI_PIPELINE_SOURCE == "web" && $CI_COMMIT_TAG == null
      when: on_success
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: on_success


lib:
  image: node:latest
  stage: lib
  extends: .common-rules
  variables:
    NPM_TOKEN: ${CI_JOB_TOKEN}
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - .npm/
      - lib/
  before_script:
    - |
      {
        echo "@${CI_PROJECT_ROOT_NAMESPACE}:registry=${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/npm/"
        echo "${CI_API_V4_URL#https?}/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}"
      } | tee --append .npmrc
    - npm ci --cache .npm --prefer-offline
  script:
    - npm run lib

publish:
  image: node:latest
  stage: release
  variables:
    NPM_TOKEN: ${CI_JOB_TOKEN}
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - .npm/
      - lib/
  before_script:
    - |
      {
        echo "@${CI_PROJECT_ROOT_NAMESPACE}:registry=${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/npm/"
        echo "${CI_API_V4_URL#https?}/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}"
      } | tee --append .npmrc
    - npm ci --cache .npm --prefer-offline
  script:
    - npm run semantic-release
    - rm -rf .npmrc
    - apt update && apt-get install expect -y
    - |
      expect <<EOD
      spawn npm login --registry=https://nexus.ops.connectedplatform.net/repository/npm-hosted/
      expect "Username:"
      send "$NEXUS_USERNAME\r"
      expect "Password:"
      send "$NEXUS_PASSWORD\r"
      expect eof
      EOD
    - npm publish --registry=https://nexus.ops.connectedplatform.net/repository/npm-hosted/
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

build:
  image: docker:stable
  services:
    - docker:dind
  stage: build
  extends: .common-rules
  before_script:
    - printenv
    - docker login -u "$OKE_REGISTRY_USER" -p "$OKE_REGISTRY_PASSWORD" $OKE_REGISTRY
    - echo "//repo.nextgenclearing.com/api/v4/packages/npm/:_authToken=${CI_JOB_TOKEN}" >> .npmrc
  script:
    - >
      docker build
      --pull -t $CONTAINER_TEST_IMAGE .
    - docker push $CONTAINER_TEST_IMAGE

latest-image:
  image: docker:stable
  services:
    - docker:dind
  stage: latest-image
  needs:
    - build
  before_script:
    - docker login -u "$OKE_REGISTRY_USER" -p "$OKE_REGISTRY_PASSWORD" $OKE_REGISTRY
  script:
    - docker pull $CONTAINER_TEST_IMAGE
    - docker tag $CONTAINER_TEST_IMAGE $CONTAINER_LATEST_IMAGE
    - docker push $CONTAINER_LATEST_IMAGE
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
