const config = {
  preset: 'ts-jest',
  setupFiles: ['<rootDir>/__mocks__/envTest.js'],
  setupFilesAfterEnv: ['<rootDir>/__mocks__/jest-setup.js'],
  moduleDirectories: [
    '<rootDir>/src',
    'node_modules',
  ],
  transform: {
    '^.+\\.(ts|tsx)?$': 'ts-jest',
    '^.+\\.(js|jsx)$': 'babel-jest',
    '.+\\.(css|scss)$': 'jest-css-modules-transform',
  },
  transformIgnorePatterns: [
    '/node_modules/(?!@nv2/nv2-pkg-js-theme).+\\.js$',
  ],
  moduleNameMapper: {
    '\\.(gif|ttf|eot|svg|png|ico)$': '<rootDir>/__mocks__/fileMock.js',
    '\\.(scss|sass|css)$': 'identity-obj-proxy',
    '^@/(.*)$': '<rootDir>/$1',
    '^core/(.*)$': '<rootDir>/src/core/$1',
    '^components/(.*)$': '<rootDir>/src/components/$1',
    '^assets/(.*)$': '<rootDir>/src/assets/$1',
  },
  testEnvironment: 'jsdom',
  reporters: ['default', 'jest-junit'],
  coverageReporters: ['cobertura'],
};

module.exports = config;
