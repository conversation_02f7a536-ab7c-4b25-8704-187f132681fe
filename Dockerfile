FROM node:16-alpine as builder

RUN mkdir /app
WORKDIR /app
COPY package.json package-lock.json .npmrc /app/

## Storing node modules on a separate layer will prevent unnecessary npm installs at each build
RUN npm i
COPY . .

## Build the react app in production mode and store the artifacts in dist folder
RUN npm run build-storybook

FROM nginx:latest
COPY nginx.conf /etc/nginx/conf.d/default.conf
RUN mkdir /app
COPY --from=builder /app/storybook-static /app/storybook

CMD ["nginx", "-g", "daemon off;"]
