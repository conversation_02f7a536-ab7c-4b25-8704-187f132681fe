# Using @nv2/nv2-pkg-js-shared-components as a dependency in a project

## Install

```bash
npm install --save @nv2/nv2-pkg-js-shared-components
```

## Usage

```jsx
import React, { Component } from 'react'

import MyComponent from '@nv2/nv2-pkg-js-shared-components'

class Example extends Component {
  render() {
    return <MyComponent />
  }
}
```

## License

MIT © [Flyaps]

# Local work with @nv2/nv2-pkg-js-shared-components 

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).
Before installing dependencies, make sure you have Node.js v16.

Project uses private gitlab package:
- [nv2-pkg-js-theme](https://gitlabnv2.flyaps.com/nv2/pkg/js/nv2-pkg-js-theme)

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `npm run lint`

[ESLint](https://github.com/eslint/eslint) statically analyzes .js and .jsx code to quickly find problems.

### `npm run storybook`

Start the component explorer on port 6006

### `npm run build-storybook`

Build Storybook

### `npm run test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm eject`

**Note: this is a one-way operation. Once you `eject`, you can’t go back!**

If you aren’t satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you’re on your own.

You don’t have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn’t feel obligated to use this feature. However we understand that this tool wouldn’t be useful if you couldn’t customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).

### Code Splitting

This section has moved here: [https://facebook.github.io/create-react-app/docs/code-splitting](https://facebook.github.io/create-react-app/docs/code-splitting)

### Analyzing the Bundle Size

This section has moved here: [https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size](https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size)

### Making a Progressive Web App

This section has moved here: [https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app](https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app)

### Advanced Configuration

This section has moved here: [https://facebook.github.io/create-react-app/docs/advanced-configuration](https://facebook.github.io/create-react-app/docs/advanced-configuration)

### Deployment

This section has moved here: [https://facebook.github.io/create-react-app/docs/deployment](https://facebook.github.io/create-react-app/docs/deployment)

### `npm build` fails to minify

This section has moved here: [https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify](https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify)

## Publish npm packages to the GitLab Package Registry using semantic-release

https://docs.gitlab.com/ee/ci/examples/semantic-release.html

https://github.com/semantic-release/semantic-release

## Work with storybook

https://storybook.js.org/docs/react



