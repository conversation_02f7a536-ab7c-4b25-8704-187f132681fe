const path = require('path');

module.exports = {
  "stories": ["../src/**/*.stories.mdx", "../src/**/*.stories.@(js|jsx|ts|tsx)"],
  "addons": ["@storybook/addon-links", "@storybook/addon-essentials", "@storybook/preset-create-react-app", "@storybook/addon-mdx-gfm"],
  typescript: {
    reactDocgen: 'react-docgen-typescript-plugin'
  },
  webpackFinal(config) {
    const sassLoaderRule = {
      test: /\.s[ac]ss$/i,
      use: [{
        loader: 'sass-loader',
        options: {
          prependData: `@import 'node_modules/@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';`
        }
      }]
    };
    config.module.rules.push(sassLoaderRule);

    config.resolve.modules = [
      path.resolve(__dirname, "..", "src"),
      "node_modules",
    ]

    return config;
  },
  framework: {
    name: "@storybook/react-webpack5",
    options: {}
  },
  docs: {
    autodocs: true
  }
};