const noop = () => {};
Object.defineProperty(window, 'scrollTo', { value: noop, writable: true });

window.HTMLCanvasElement.prototype.getContext = () => {};

jest.mock('@nv2/nv2-pkg-js-theme/src/components/getBrandColors', () => {
  const getBrandColors = () => ({
    50: '#000000',
    100: '#000000',
    300: '#000000',
    500: '#000000',
  });
  return getBrandColors;
});

global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});
