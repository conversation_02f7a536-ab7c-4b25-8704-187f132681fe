import React from 'react';
import { render, screen } from '@testing-library/react';
import App from './App';

jest.mock('@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss', () => ({
  brandBlueColor500: '#66dbff',
  yellowColor500: '#e3d91c',
  darkColor500: '#020202',
}));

test('renders learn react link', () => {
  render(
    <App />,
  );
  const breadcrumbs = screen.getByLabelText('breadcrumb');
  expect(breadcrumbs).toBeInTheDocument();
});
