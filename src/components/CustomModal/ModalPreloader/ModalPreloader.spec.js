import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import ModalPreloader from './ModalPreloader';

describe('ModalPreloader', () => {
  test('should be ModalPreloader in te DOM ', () => {
    render(<ModalPreloader />);

    const modalPreloader = screen.getByTestId('custom-modal__preloader');

    expect(modalPreloader).toBeInTheDocument();
  });

  test('should renders CircularProgress when showCircularProgress is true', () => {
    render(<ModalPreloader showCircularProgress />);

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('should not render CircularProgress when showCircularProgress is false', () => {
    render(<ModalPreloader showCircularProgress={false} />);

    expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
  });
});
