import React from 'react';
import PropTypes from 'prop-types';
import { CircularProgress } from '@mui/material';

const ModalPreloader = ({ showCircularProgress }) => (
  <div
    className="custom-modal__preloader"
    data-testid="custom-modal__preloader"
  >
    {showCircularProgress && <CircularProgress />}
  </div>
);

ModalPreloader.propTypes = {
  showCircularProgress: PropTypes.bool,
};

ModalPreloader.defaultProps = {
  showCircularProgress: true,
};

export default ModalPreloader;
