$overlay-background: rgba(255, 255, 255, 0.5);

.custom-modal {
  &__wrap {
    box-shadow: $shadow16;
    padding: 32px;
    color: $dark-color-500;
  }

  &__title {
    &-wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: inherit;
      color: $dark-color-500;
    }
  }

  &__close-btn {
    width: 32px !important;
    height: 32px !important;
  }

  &__btn {
    width: 106px;

    &-confirm {
      margin-right: 20px !important;
    }

    &-wrap {
      margin-top: 20px;
    }
  }

  .MuiDialogContent-root {
    padding: 0 !important;
    overflow: hidden;
  }

  .MuiDialogTitle-root {
    padding: 0 !important;
  }

  .MuiDialog-paperScrollPaper {
    max-height: 100% !important;
    overflow: hidden;
  }

  .MuiDialog-paper {
    margin: 20px !important;
  }

  .pre-loading-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 5;
    background-color: $overlay-background;
  }
}
