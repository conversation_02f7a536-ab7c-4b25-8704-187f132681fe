import React from 'react';
import { GrClose } from 'react-icons/gr';
import PropTypes from 'prop-types';
import {
  Button, CircularProgress,
  Dialog, DialogContent, IconButton, Typography,
} from '@mui/material';
import ModalPreloader from './ModalPreloader';
import './CustomModal.scss';

const CustomModal = ({
  isOpen, handleOpen, title, children, dataTestid, modalClass,
  onClickConfirm, isLoading, confirmButtonName,
  buttonDisable, showButtons, onClickCancel, subTitle, showConfirmButtonPreloader,
  showPreloader, showCircularProgressInPreloader, preloaderComponent,
  disableBackdropClick = false, ...props
}) => {
  const closeModal = () => { handleOpen(false); };

  const handleCloseWithBackdropCheck = (event, reason) => {
    if (disableBackdropClick && reason === 'backdropClick') return;
    closeModal();
  };

  const preloader = preloaderComponent || (
    <ModalPreloader showCircularProgress={showCircularProgressInPreloader} />
  );

  return (
    <Dialog
      open={isOpen}
      onClose={handleCloseWithBackdropCheck}
      className="custom-modal"
      data-testid={dataTestid}
      aria-labelledby="simple-dialog-title"
      maxWidth="xl"
      {...props}
    >
      <div className={`custom-modal__wrap ${modalClass}`}>
        <div
          className="custom-modal__title-wrap"
        >
          <Typography className="custom-modal__title" variant="h3">{title}</Typography>

          <IconButton
            className="custom-modal__close-btn"
            onClick={closeModal}
          >
            <GrClose size={18} />
          </IconButton>
        </div>
        <Typography variant="body1" component="p" className="custom-modal__subtitle">
          {subTitle}
        </Typography>
        {isLoading && showPreloader && preloader}
        <DialogContent className="custom-modal__content">
          {children}
        </DialogContent>
        {showButtons && (
          <div
            className="custom-modal__btn-wrap"
          >
            <Button
              variant="contained"
              color="primary"
              className="custom-modal__btn custom-modal__btn-confirm"
              onClick={onClickConfirm}
              disabled={buttonDisable}
              data-testid="confirm-btn"
            >
              {(isLoading && showConfirmButtonPreloader) ? <CircularProgress size={25} sx={{ color: 'white' }} /> : confirmButtonName}
            </Button>
            <Button
              variant="outlined"
              color="primary"
              onClick={onClickCancel}
              className="custom-modal__btn custom__btn-cancel"
            >
              Cancel
            </Button>
          </div>
        )}
      </div>
    </Dialog>
  );
};

CustomModal.propTypes = {
  isOpen: PropTypes.bool,
  handleOpen: PropTypes.func,
  title: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.element,
  ]),
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
  ]).isRequired,
  dataTestid: PropTypes.string,
  modalClass: PropTypes.string,
  onClickCancel: PropTypes.func,
  onClickConfirm: PropTypes.func,
  showButtons: PropTypes.bool,
  isLoading: PropTypes.bool,
  buttonDisable: PropTypes.bool,
  confirmButtonName: PropTypes.string,
  subTitle: PropTypes.string,
  showPreloader: PropTypes.bool,
  showCircularProgressInPreloader: PropTypes.bool,
  preloaderComponent: PropTypes.node,
  showConfirmButtonPreloader: PropTypes.bool,
  disableBackdropClick: PropTypes.bool,
};

CustomModal.defaultProps = {
  isOpen: false,
  handleOpen: () => {},
  modalClass: '',
  dataTestid: 'custom-modal',
  buttonDisable: false,
  isLoading: false,
  confirmButtonName: 'Confirm',
  showButtons: true,
  onClickCancel: () => {},
  onClickConfirm: () => {},
  subTitle: '',
  title: undefined,
  showPreloader: true,
  showCircularProgressInPreloader: true,
  preloaderComponent: null,
  showConfirmButtonPreloader: true,
  disableBackdropClick: false,
};
export default CustomModal;
