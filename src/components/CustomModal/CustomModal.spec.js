import React, { useState } from 'react';
import userEvent from '@testing-library/user-event';
import { render, waitFor } from '@testing-library/react';
import UserAvatar from 'components/UserAvatar';
import CustomModal from './CustomModal';

let mockProps = {
  showButtons: true,
  buttonDisable: false,
  subTitle: 'test SubTitle',
  title: 'Test Title',
};

const TestComponent = (props) => {
  const [open, setOpen] = useState(true);

  return (
    <CustomModal
      isOpen={open}
      handleOpen={setOpen}
      {...mockProps}
      {...props}
    >
      <div>Test Content</div>
      <UserAvatar primaryColor="#fff" userName="Name" />
    </CustomModal>
  );
};

describe('Custom Modal', () => {
  test('should be element with id = "custom-modal" ', () => {
    const { getByTestId } = render(<TestComponent />);

    expect(getByTestId('custom-modal')).toBeInTheDocument();
  });

  test('should be "Test Title" in the Document', () => {
    const { getByText } = render(<TestComponent />);

    expect(getByText('Test Title')).toBeInTheDocument();
  });

  test('should render custom element title in the Document', () => {
    const ElementTitle = () => (
      <div>
        <span>Custom Element Title</span>
      </div>
    );

    const { getByText } = render(
      <TestComponent title={<ElementTitle />} />,
    );

    expect(getByText('Custom Element Title')).toBeInTheDocument();
  });

  test('should be "Test Content" in the Document', () => {
    const { getByText } = render(<TestComponent />);

    expect(getByText('Test Content')).toBeInTheDocument();
  });

  test('should be SubTitle in the Document ', () => {
    const { getByText } = render(<TestComponent {...mockProps} />);
    const subTitle = getByText(mockProps.subTitle);

    expect(subTitle).toBeInTheDocument();
  });

  test('should be close if click outside Modal window', () => {
    const { getByTestId } = render(
      <TestComponent />,
    );
    const backdrop = document.querySelector('.MuiBackdrop-root');

    userEvent.click(backdrop);

    waitFor(() => {
      expect(getByTestId('custom-modal')).not.toBeInTheDocument();
    });
  });

  test('should be Disabled "Confirm" button ', () => {
    mockProps = { ...mockProps, buttonDisable: true, confirmButtonName: 'Confirm' };

    const { getByText } = render(<TestComponent />);
    const cancelButton = getByText('Confirm').closest('button');

    expect(cancelButton).toBeDisabled();
  });

  test('should not render Confirm and Cancel buttons', () => {
    mockProps = { ...mockProps, showButtons: false };

    render(<TestComponent {...mockProps} />);
    const buttons = document.querySelector('.custom-modal__btn-wrap');

    expect(buttons).not.toBeInTheDocument();
  });

  test.each`
    isLoading | showConfirmButtonPreloader | shouldShowPreloader
    ${true}   | ${true}                    | ${true}
    ${true}   | ${false}                   | ${false}
    ${false}  | ${true}                    | ${false}
  `('should show preloader: $shouldShowPreloader when isLoading is $isLoading and showConfirmButtonPreloader is $showConfirmButtonPreloader', ({ isLoading, showConfirmButtonPreloader, shouldShowPreloader }) => {
    const { getByTestId } = render(
      <TestComponent
        isLoading={isLoading}
        showConfirmButtonPreloader={showConfirmButtonPreloader}
        showButtons
      />,
    );

    const confirmButton = getByTestId('confirm-btn');

    const preloader = confirmButton.querySelector('.MuiCircularProgress-root');

    if (shouldShowPreloader) {
      expect(preloader).toBeInTheDocument();
    } else {
      expect(preloader).not.toBeInTheDocument();
    }
  });
});
