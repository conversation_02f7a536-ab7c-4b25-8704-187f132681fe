import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import { unstable_HistoryRouter as HistoryRouter } from 'react-router-dom';
import { createMemoryHistory } from 'history';

import PageNotFound from './PageNotFound';

describe('PageNotFound:', () => {
  const route = '/test';
  const homeRoute = '/';
  const history = createMemoryHistory({ initialEntries: [route] });
  const getCurrentThemeColors = () => ({
    100: '',
    200: '',
  });

  const mockProps = {
    primaryColor: '#000000',
    getCurrentThemeColors,
  };

  const pageNotFound = (
    <HistoryRouter history={history}>
      <PageNotFound {...mockProps} />
    </HistoryRouter>
  );
  test('should be redirect button', () => {
    const { getByTestId } = render(pageNotFound);
    const btn = getByTestId('page-not-found__btn');

    expect(btn).toBeInTheDocument();
  });

  test('should redirects to landing(home)', () => {
    const { getByTestId } = render(pageNotFound);
    const btn = getByTestId('page-not-found__btn');

    fireEvent.click(btn);

    expect(history.location.pathname).toEqual(homeRoute);
  });
});
