import React from 'react';

import { Button, Typography } from '@mui/material';
import { Link as RouteLink } from 'react-router-dom';

import PageNotFoundIcon from './PageNotFoundIcon';

interface IPageNotFoundProps {
  primaryColor: string
  getCurrentThemeColors: (color: string) => { [key: string]: string }
  secondaryColor?: string | null
}

const PageNotFound = ({
  getCurrentThemeColors, primaryColor, secondaryColor,
}: IPageNotFoundProps) => (
  // for bt primaryColor is secondaryColor; secondaryColor is primaryColor
  <div className="page-not-found__wrap">
    <div className="page-not-found">
      <PageNotFoundIcon
        primaryColor={primaryColor}
        getCurrentThemeColors={getCurrentThemeColors}
        secondaryColor={secondaryColor}
      />
      <Typography
        variant="h2"
        color={getCurrentThemeColors(primaryColor)[100]}
        component="h2"
        className="page-not-found__error"
      >
        404
      </Typography>
      <Typography variant="h3" component="h4" className="page-not-found__title">Page not found.</Typography>
      <Typography variant="body1" component="p" className="page-not-found__text">
        This page doesn’t exist or was removed.
      </Typography>
      <Button
        component={RouteLink}
        variant="contained"
        sx={{
          backgroundColor: secondaryColor || primaryColor,
        }}
        className="page-not-found__btn"
        data-testid="page-not-found__btn"
        to="/"
      >
        Go to the Homepage
      </Button>
    </div>
  </div>
);

PageNotFound.defaultProps = {
  secondaryColor: null,
};

export default PageNotFound;
