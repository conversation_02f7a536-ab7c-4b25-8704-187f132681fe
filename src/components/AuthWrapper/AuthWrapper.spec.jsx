import React from 'react';
import Forbidden from 'components/Forbidden';
import AuthWrapper from './AuthWrapper';
import permissions from './permission';

test('valid permission, repository, and access', () => {
  const permission = ['Create Account'];
  const repository = ['AccountManagement'];

  const result = AuthWrapper({
    children: <div>Authorized</div>,
    permission,
    repository,
    isComponent: false,
    access: permissions,
  });

  expect(result).toEqual(<div>Authorized</div>);
});

test('invalid permission and valid repository', () => {
  const permission = ['invalidPermission'];
  const repository = ['AccountManagement', 'MarketShareReport'];

  const result = AuthWrapper({
    children: <div>Authorized</div>,
    permission,
    repository,
    isComponent: false,
    access: permissions,
  });

  expect(result).toEqual(null);
});

test('valid permission and invalid repository', () => {
  const permission = ['Create Account'];
  const repository = ['xyz'];

  const result = AuthWrapper({
    children: <div>Authorized</div>,
    permission,
    repository,
    isComponent: false,
    access: permissions,
  });

  expect(result).toEqual(null);
});

test('valid permission, repository not valid, and  access: permissions', () => {
  const permission = ['Create Account'];
  const repository = ['AccountManagement1'];

  const result = AuthWrapper({
    children: <div>Authorized</div>,
    permission,
    repository,
    isComponent: true,
    access: permissions,
  });

  expect(result).not.toEqual(<Forbidden />);
});

test('not valid permission, repository valid, and  access: permissions', () => {
  const permission = ['Create Account1'];
  const repository = ['AccountManagement'];

  const result = AuthWrapper({
    children: <div>Authorized</div>,
    permission,
    repository,
    isComponent: true,
    access: permissions,
  });

  expect(result).not.toEqual(<Forbidden />);
});

test('valid permission, repository valid, and  not access: permissions', () => {
  const permission = ['Create Account'];
  const repository = ['AccountManagement'];

  const result = AuthWrapper({
    children: <div>Authorized</div>,
    permission,
    repository,
    isComponent: true,
    access: [],
  });

  expect(result).not.toEqual(<Forbidden />);
});

test('valid permission, repository, and  access: permissions, but unauthorized', () => {
  const permission = ['Create Account'];
  const repository = ['AccountManagement'];

  const result = AuthWrapper({
    children: <div>Authorized</div>,
    permission,
    repository,
    isComponent: true,
    access: permissions,
  });

  expect(result).toEqual(<div>Authorized</div>);
});

test('empty permission and repository', () => {
  const permission = [];
  const repository = [];

  const result = AuthWrapper({
    children: <div>Authorized</div>,
    permission,
    repository,
    isComponent: false,
    access: permissions,
  });

  expect(result).toEqual(<div>Authorized</div>);
});

test('no permission and repository', () => {
  const result = AuthWrapper({
    children: <div>Authorized</div>,
    isComponent: false,
    access: permissions,
  });

  expect(result).toEqual(<div>Authorized</div>);
});

test('no access', () => {
  const result = AuthWrapper({
    children: <div>Authorized</div>,
    permission: 'read',
    repository: 'myRepo',
    isComponent: false,
  });

  expect(result).toEqual(null);
});
