import PropTypes from 'prop-types';
import React from 'react';
import useAuthorization from './useAuthorization';

const AuthWrapper = ({
  children, permission, repository, isComponent, access, Forbidden,
}) => {
  const hasPermissionAndRepository = (permission
        && permission?.length !== 0) && (repository && repository?.length !== 0);

  if (hasPermissionAndRepository) {
    const isAuthorise = useAuthorization(permission, repository, access);
    if (!isAuthorise && isComponent) {
      return <Forbidden />;
    }

    return isAuthorise ? children : null;
  }

  return children;
};

AuthWrapper.defaultProps = {
  isComponent: PropTypes.bool,
  Forbidden: () => <div>Forbidden</div>,
};

AuthWrapper.propTypes = {
  children: PropTypes.oneOfType([PropTypes.element,
    PropTypes.arrayOf(PropTypes.element)]).isRequired,
  permission: PropTypes.arrayOf(PropTypes.string).isRequired,
  repository: PropTypes.arrayOf(PropTypes.string).isRequired,
  isComponent: PropTypes.bool,
  access: PropTypes.arrayOf(PropTypes.string).isRequired.isRequired,
  Forbidden: PropTypes.element,
};

export default AuthWrapper;
