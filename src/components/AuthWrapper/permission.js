const permissions = [
  {
    name: 'SIMManagement',
    permission: [
      {
        id: '421ebc67-6418-41eb-be3d-b1f8f5f58700',
        name: 'Get SIM Remains',
        title: 'Get SIM Remains',
      },
      {
        id: 'ff9d04db-ca04-4370-a2dd-fb4856027e8b',
        name: 'Create Range',
        title: 'Create Range',
      },
      {
        id: 'b1761a2e-8ee2-4bcc-bb71-5cb7984be999',
        name: 'Create Allocation',
        title: 'create_allocation',
      },
      {
        id: '50cddd6f-ef93-4934-b036-da24b39c1c10',
        name: 'SIM Cease API',
        title: 'sim_deactivate_api',
      },
      {
        id: '30270f6b-26ae-48c6-9f2e-bb659913c1f7',
        name: 'SIM Cease',
        title: 'sim_deactivate',
      },
      {
        id: '7bd1131d-3590-45e3-ab5f-b4d9078510bc',
        name: 'Get Voice Connection History',
        title: 'voice_connection_history',
      },
      {
        id: 'c591aacb-a1a6-4e9a-8a7c-0112977f7ea3',
        name: 'Delete Allocations By Range',
        title: 'remove_allocations',
      },
      {
        id: 'acc1488e-a9e3-4e3b-98ea-836ab6d7e8c5',
        name: 'Get Account Names',
        title: 'get_account_names',
      },
      {
        id: '2b3ccc58-868e-4752-8240-9ce89f06c99e',
        name: 'Delete Range',
        title: 'delete_range',
      },
      {
        id: '********-918a-4f96-90e1-2942f941725b',
        name: 'Export Connection History',
        title: 'convert_response_to_csv_history',
      },
      {
        id: '5d1a1308-36c1-453f-b7c2-527621ec4c53',
        name: 'SIM Provide',
        title: 'sim_activate',
      },
      {
        id: '28c8dd89-f2f3-4487-8b0d-e0ebb4dff72b',
        name: 'Get SIM Management',
        title: 'get_sims_usage',
      },
      {
        id: 'f2b5f53b-771a-4198-befa-1e1af2bde24f',
        name: 'Get Connection History',
        title: 'connection_history',
      },
      {
        id: 'cee292c8-fe9f-4f53-8850-fe45018fcdde',
        name: 'Get SMS Connection History',
        title: 'sms_connection_history',
      },
      {
        id: '573e5e7e-f32e-4463-9008-45c7b6ba11ab',
        name: 'Export SIM Management',
        title: 'convert_response_to_csv_usage',
      },
      {
        id: 'f262cfa7-ba17-4320-a080-0b6a78d1a782',
        name: 'Get Ranges',
        title: 'get_ranges',
      },
      {
        id: '613bcf0a-1c14-4bee-aa8e-d7360e4cc281',
        name: 'Delete Allocation',
        title: 'remove_allocation',
      },
      {
        id: '0d6b35eb-4b59-43a2-9156-9ab1b4239f14',
        name: 'Get SIM Status',
        title: 'get_sim_status',
      },
      {
        id: '37629882-72e7-4d35-83bd-c13e16435345',
        name: 'SIM Audit Logs',
        title: 'audit_logs',
      },
      {
        id: '958446dc-59de-4350-9ba7-3aefc4dfd9aa',
        name: 'Get SIM Status API',
        title: 'get_sim_status_api',
      },
      {
        id: '09603637-d8e0-4b89-8dea-dfa5d56c7764',
        name: 'Get Allocations',
        title: 'get_allocations',
      },
      {
        id: '0bee60b1-cfb0-4f60-a1f7-854de796acde',
        name: 'SIM Provide API',
        title: 'sim_activate_api',
      },
      {
        id: '8e1e0d1e-0a6f-4207-a583-b0817ed477d7',
        name: 'SIM Connection Summary',
        title: 'sim_connection_summary',
      },
    ],
  },
  {
    name: 'UserManagement',
    permission: [
      {
        id: '33a512f9-870f-4b73-aa6e-2948a762e2e3',
        name: 'Invite User',
        title: 'invite_user',
      },
    ],
  },
  {
    name: 'Billing',
    permission: [
      {
        id: 'e3e10817-d548-46d5-b844-e9a566afc029',
        name: 'View Invoices',
        title: 'get_invoices',
      },
      {
        id: 'fc5cd329-863a-4a5d-bfaa-6479c5670596',
        name: 'Generate Invoices',
        title: 'generate_billing_cycle_invoices',
      },
      {
        id: 'aa921345-503c-4118-8e2e-c66c4c8f94b4',
        name: 'Publish Invoice',
        title: 'publish_invoice',
      },
      {
        id: 'f4177f7b-700b-4a21-b8c7-7108a23b122f',
        name: 'Modify Adjustment',
        title: 'update_adjustment',
      },
      {
        id: 'f6a44e8c-209c-47d3-bbb7-dc0f31bc510e',
        name: 'Delete Adjustment',
        title: 'remove_adjustment',
      },
      {
        id: '2a0d5686-f367-4653-b1c7-4a557fa417a4',
        name: 'Get Invoice Detail',
        title: 'invoice_details',
      },
      {
        id: '1d2d1010-05c4-4b51-a1d1-14c469386c43',
        name: 'Export Invoice Usage',
        title: 'convert_response_to_csv_invoice_usage',
      },
      {
        id: 'aa360fda-f089-41b8-bdee-5e2a946600d3',
        name: 'Create Adjustment',
        title: 'create_adjustment',
      },
      {
        id: '8747ae98-5312-4d0b-a6ff-30d02e62f1e5',
        name: 'Get Invoice Usage',
        title: 'invoice_usage',
      },
    ],
  },
  {
    name: 'Authorization',
    permission: [
      {
        id: 'aa314052-dac2-4c3e-8a19-cf56d3c28812',
        name: 'Get Group Roles',
        title: 'get_group_role',
      },
      {
        id: '81470740-0790-458f-b705-2a1c26b0ba30',
        name: 'Get Group Users',
        title: 'get_group_members',
      },
      {
        id: '36d38f89-5138-45cc-9eb2-66599d7e866f',
        name: 'Get Resource Scope',
        title: 'get_resource_scopes',
      },
      {
        id: '03fe22b8-7aaf-48f8-a3ca-6c8e8a024b19',
        name: 'Get User Scope',
        title: 'get_users_scopes',
      },
      {
        id: 'd00d2958-fb76-47d2-b62d-2946ef4d9ed0',
        name: 'Get Roles',
        title: 'get_roles',
      },
      {
        id: '96e070ce-69d9-4feb-b254-ef6f0a082efa',
        name: 'Get Permission By Role',
        title: 'get_permissions_role_id',
      },
    ],
  },
  {
    name: 'RatePlan',
    permission: [
      {
        id: '1ba1c605-9856-48cc-bb57-eeff91b0076c',
        name: 'Get Rate Plans',
        title: 'rate_plans_by_accounts',
      },
      {
        id: '1ba1c605-9856-48cc-bb57-eeff91b0076c',
        name: 'Account Rate Plans',
        title: 'account_rate_plans',
      },
      {
        id: 'cf4ee508-d57b-4e93-ac51-379208cbf538',
        name: 'Modify Rate Plan',
        title: 'update_rate_plan',
      },
      {
        id: '5c8f88ce-870c-422b-847f-0463f01109e5',
        name: 'Get Rate Plan',
        title: 'rate_plan_details',
      },
      {
        id: '41dd3ea3-2cd2-4443-8d64-914c0abb50a6',
        name: 'Create Rate Plan',
        title: 'create_rate_plan',
      },
      {
        id: '82965e8e-2712-4a23-9196-bf9f68653aac',
        name: 'Delete Rate Plan',
        title: 'remove_rate_plan',
      },
    ],
  },
  {
    name: 'MarketShareReport',
    permission: [
      {
        id: '7f0b7970-06f0-4a0c-90f6-5ea93ae80ebf',
        name: 'Market Share For Account',
        title: 'get_market_share_by_account',
      },
      {
        id: 'fe5a668d-e73d-4cb7-9ef5-df49d25cea7a',
        name: 'Market Share For SIM',
        title: 'get_market_share_imsi',
      },
      {
        id: 'a852c272-98a5-4ca6-a878-3696a100ab93',
        name: 'Overall Market Share',
        title: 'get_market_share',
      },
    ],
  },
  {
    name: 'AccountManagement',
    permission: [
      {
        id: '0a2beb21-5599-481b-8c78-39be6a148e25',
        name: 'Create Account',
        title: 'Create Account',
      },
      {
        id: 'e6781bb9-9d3d-4fe2-a9de-e233a8c4726a',
        name: 'Modify Account',
        title: 'Modify Account',
      },
      {
        id: '1d21083a-f677-4e38-9ff9-78832cc0b159',
        name: 'View Accounts',
        title: 'list_accounts',
      },
      {
        id: '66a89bd8-9186-4ae4-90a6-8f2cb8504c52',
        name: 'Delete Account',
        title: 'Delete Account',
      },
      {
        id: '2bfe230b-59c2-4307-8ad4-e54768aea424',
        name: 'View Account Details',
        title: 'view_account',
      },
    ],
  },
  {
    name: 'AuditLog',
    permission: [
      {
        id: '232a965c-fcff-4fa2-811a-80685da27de7',
        name: 'Account Audit Logs',
        title: 'system_audit_logs',
      },
    ],
  },
];
export default permissions;
