const usePermission = (repoName, access) => {
  let permission = [];

  if (access && access?.length > 0) {
    const accessValue = access.filter((x) => repoName.includes(x.name));
    if (accessValue) {
      const finalValue = [];
      accessValue?.map((x) => x.permission).reduce((result, arr) => {
        arr?.forEach((obj) => {
          finalValue.push(obj);
        });
        return result;
      }, {});
      permission = JSON.parse(JSON.stringify(finalValue));
    }
  }

  return permission;
};
export default usePermission;
