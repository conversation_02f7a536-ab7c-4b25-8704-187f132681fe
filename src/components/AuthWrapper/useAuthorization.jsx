import PropTypes from 'prop-types';
import usePermission from './usePermission';

const useAuthorization = (rights, repoName, access) => {
  const permission = usePermission(repoName, access);

  let authorized = false;
  if (rights && permission && permission.length > 0) {
    // Check if 'rights' is in the 'permission' array
    const finalValueNames = permission.map((t) => t.name);
    // Check if 'rights' is in the 'permission' array
    const allow = rights.every((name) => finalValueNames.includes(name));

    // Only update 'isAuthorise' if 'allow' changes
    if (authorized !== allow) {
      authorized = allow;
    }
  } else {
    authorized = false;
  }

  return authorized;
};

useAuthorization.propTypes = {
  rights: PropTypes.arrayOf(PropTypes.string).isRequired,
  repoName: PropTypes.arrayOf(PropTypes.string).isRequired,
  access: PropTypes.arrayOf(PropTypes.string).isRequired.isRequired,

};

export default useAuthorization;
