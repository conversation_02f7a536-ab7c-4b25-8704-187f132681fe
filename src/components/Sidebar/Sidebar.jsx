import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Typography } from '@mui/material';
import {
  Sidebar as ProSidebar, Menu, SubMenu, menuClasses,
} from 'react-pro-sidebar';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import { GrClose, GrDown, GrUp } from 'react-icons/gr';
import SidebarMenuItem from './SidebarMenuItem';
import SidebarMenuItemWrapper from './SidebarMenuItemWrapper';
import { getMenuItemStyles } from './styles';
import './Sidebar.scss';

const Sidebar = ({
  primaryColor,
  secondaryColor,
  getCurrentThemeColors,
  sidebarConfig,
  activeLink,
  appName,
  toggleSidebar,
  collapsed,
  toggled,
  showTooltip,
}) => {
  const [tooltipAction, setTooltipAction] = useState({});

  const menuItemStyles = getMenuItemStyles(secondaryColor, getCurrentThemeColors);

  const handleTooltipClose = (id) => {
    setTooltipAction((prev) => ({ ...prev, [id]: false }));
  };

  const handleTooltipOpen = (id) => {
    setTooltipAction((prev) => ({ ...prev, [id]: true }));
  };

  const getSubMenuLinks = (links) => links.map((link) => (
    <SidebarMenuItem
      key={link.id.toString()}
      title={link.name}
      path={link.path}
      className="sidebar-submenu__link-name"
    />
  ));

  const links = sidebarConfig.map((item, index) => {
    const isActive = item.id === activeLink;
    const componentId = item.id.toString() || index;

    if (item.submenu) {
      return (
        <SidebarMenuItemWrapper
          withTooltip={showTooltip && collapsed}
          id={componentId}
          key={componentId}
          title={item.name}
          open={tooltipAction[componentId] || false}
          onClose={() => handleTooltipClose(componentId)}
          onOpen={() => handleTooltipOpen(componentId)}
        >
          <SubMenu
            key={componentId}
            icon={item.icon}
            label={item.name}
            className="sidebar-submenu sidebar__link-name"
            data-testid="pro-sidebar-submenu"
            active={isActive}
            onClick={() => handleTooltipClose(componentId)}
            rootStyles={{
              [`& > .${menuClasses.button}`]: {
                backgroundColor: getCurrentThemeColors(primaryColor)[700],
              },
              [`.${menuClasses.subMenuContent}`]: {
                backgroundColor: getCurrentThemeColors(primaryColor)[700],
              },
            }}
          >
            {getSubMenuLinks(item.submenu, isActive)}
          </SubMenu>
        </SidebarMenuItemWrapper>
      );
    }

    return (
      <SidebarMenuItem
        key={componentId}
        icon={item.icon}
        isActive={isActive}
        title={item.name}
        path={item.path}
        collapsed={collapsed}
        className="sidebar__link-name"
        showTooltip={showTooltip}
      />
    );
  });

  return (
    <ProSidebar
      backgroundColor={getCurrentThemeColors(primaryColor)[700]}
      collapsed={collapsed}
      toggled={toggled}
      breakPoint="xl"
      className="sidebar"
      width="300px"
      collapsedWidth="50px"
      onToggle={toggleSidebar}
      data-testid="pro-sidebar"
      onBackdropClick={toggleSidebar}
    >
      <Menu
        renderExpandIcon={({ open }) => {
          if (!collapsed) {
            return open ? <GrUp className="sidebar-submenu__open-icon" />
              : <GrDown className="sidebar-submenu__open-icon" />;
          }
          return null;
        }}
        menuItemStyles={{ ...menuItemStyles }}
      >
        <div className="sidebar__header">
          <Typography variant="h3" component="span">{appName}</Typography>
          <GrClose onClick={() => toggleSidebar()} className="sidebar__close-btn" size={24} />
        </div>
        {links}
      </Menu>
    </ProSidebar>

  );
};

Sidebar.propTypes = {
  activeLink: PropTypes.string.isRequired,
  primaryColor: PropTypes.string.isRequired,
  secondaryColor: PropTypes.string.isRequired,
  getCurrentThemeColors: PropTypes.func,
  sidebarConfig: PropTypes.instanceOf(Object).isRequired,
  appName: PropTypes.string.isRequired,
  toggleSidebar: PropTypes.func,
  collapsed: PropTypes.bool,
  toggled: PropTypes.bool,
  showTooltip: PropTypes.bool,
};

Sidebar.defaultProps = {
  getCurrentThemeColors: (hex) => getBrandColors(hex),
  toggleSidebar: () => {},
  collapsed: true,
  toggled: false,
  showTooltip: true,
};

export default Sidebar;
