import { alpha } from '@mui/material/styles';

export const getStyleActive = (secondaryColor, getCurrentThemeColors) => {
  const color = getCurrentThemeColors(secondaryColor)[500];
  return {
    borderLeftWidth: 2,
    borderLeftStyle: 'solid',
    borderLeftColor: `${color} !important`,
    color: `${color} !important`,
    '& .sidebar__link-name': {
      color: `${color} !important`,
    },
  };
};

export const getMenuItemStyles = (secondaryColor, getCurrentThemeColors) => {
  const color = getCurrentThemeColors(secondaryColor)[500];
  const styleActive = getStyleActive(secondaryColor, getCurrentThemeColors);

  return {
    button: ({ active }) => (active
      ? {
        ...styleActive,
        backgroundColor: alpha(color, 0.15),
        '&:hover': {
          backgroundColor: alpha(color, 0.15),
        },
        '& path': {
          color,
          stroke: color,
        },
      }
      : {
        borderLeftWidth: 2,
        borderLeftStyle: 'solid',
        borderLeftColor: 'transparent',
        '&:hover': {
          ...styleActive,
          backgroundColor: 'transparent',
          '&:hover path': {
            color,
            stroke: color,
          },
        },
      }),
  };
};
