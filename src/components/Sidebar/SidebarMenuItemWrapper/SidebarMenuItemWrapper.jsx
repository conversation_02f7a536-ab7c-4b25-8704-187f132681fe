import React from 'react';
import { Tooltip } from '@mui/material';
import PropTypes from 'prop-types';

const SidebarMenuItemWrapper = ({
  withTooltip, children, title, ...props
}) => {
  if (!withTooltip) return children;

  return (
    <Tooltip title={title} placement="right" arrow {...props}>
      <span>
        {children}
      </span>
    </Tooltip>
  );
};

SidebarMenuItemWrapper.propTypes = {
  withTooltip: PropTypes.bool.isRequired,
  children: PropTypes.node.isRequired,
  title: PropTypes.string.isRequired,
};

export default SidebarMenuItemWrapper;
