import * as React from 'react';
import PropTypes from 'prop-types';
import { MenuItem } from 'react-pro-sidebar';
import { Typography } from '@mui/material';
import { Link } from 'react-router-dom';
import SidebarMenuItemWrapper from '../SidebarMenuItemWrapper';

const SidebarMenuItem = ({
  icon, isActive, title, path, collapsed, className, showTooltip,
}) => (
  <SidebarMenuItemWrapper title={title} withTooltip={collapsed && showTooltip}>
    <MenuItem
      icon={icon}
      className="sidebar__link-name"
      active={isActive}
      component={<Link to={path} />}
    >
      <Typography variant="body1" component="span" className={className}>{title}</Typography>
    </MenuItem>
  </SidebarMenuItemWrapper>
);

SidebarMenuItem.propTypes = {
  icon: PropTypes.instanceOf(Object),
  isActive: PropTypes.bool,
  collapsed: PropTypes.bool,
  title: PropTypes.string,
  path: PropTypes.string,
  className: PropTypes.string,
  showTooltip: PropTypes.bool,
};

SidebarMenuItem.defaultProps = {
  title: '',
  path: '',
  icon: null,
  className: '',
  collapsed: false,
  isActive: false,
  showTooltip: true,
};

export default SidebarMenuItem;
