import React from 'react';
import PropTypes from 'prop-types';
import { GrMenu } from 'react-icons/gr';
import { withStyles } from '@mui/styles';
import { IconButton } from '@mui/material';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

import './SidebarToggle.scss';

const SidebarToggle = ({
  getCurrentThemeColors,
  primaryColor,
  collapseSidebar,
  toggleSidebar,
  collapsed,
}) => {
  const mobHandleToggleSidebar = (type) => {
    if (collapsed) {
      collapseSidebar(type);
    }
    toggleSidebar();
  };

  const StyledIconButton = withStyles({
    root: {
      '&:hover': {
        backgroundColor: getCurrentThemeColors(primaryColor)[50],
      },
    },
  })(IconButton);

  return (
    <>
      <StyledIconButton
        onClick={() => collapseSidebar()}
        className="sidebar-toggle-btn"
      >
        <GrMenu size={24} className="" />
      </StyledIconButton>
      <StyledIconButton
        onClick={() => mobHandleToggleSidebar('mob')}
        className="sidebar-toggle-btn_mob"
      >
        <GrMenu size={24} />
      </StyledIconButton>
    </>
  );
};

SidebarToggle.propTypes = {
  primaryColor: PropTypes.string.isRequired,
  getCurrentThemeColors: PropTypes.func,
  collapseSidebar: PropTypes.func,
  toggleSidebar: PropTypes.func,
  collapsed: PropTypes.bool,
};

SidebarToggle.defaultProps = {
  getCurrentThemeColors: (hex) => getBrandColors(hex),
  collapseSidebar: () => {},
  toggleSidebar: () => {},
  collapsed: true,
};

export default SidebarToggle;
