import React, { useState } from 'react';
import { AiOutlineDollar } from 'react-icons/ai';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import userEvent from '@testing-library/user-event';
import {
  render, waitFor, screen, getAllByRole,
} from '@testing-library/react';
import { Button } from '@mui/material';
import { BrowserRouter } from 'react-router-dom';
import { createMemoryHistory } from 'history';
import Sidebar from './index';

const sidebarConfig = [
  {
    id: 'test',
    name: 'Test',
    path: '/test',
    icon: <AiOutlineDollar style={{ fontSize: '24px' }} />,
    submenu: [{
      id: 'submenu',
      name: 'Submenu Test',
      path: '/submenu',
    },
    ],
  },
  {
    id: 'test-list',
    name: 'Test List',
    path: '/list',
    icon: <AiOutlineDollar style={{ fontSize: '24px' }} />,
  },
];

const collapsedButton = 'Collapsed Button';

const TestComponent = () => {
  const [toggled, setToggled] = useState(false);
  const toggleSidebar = () => setToggled(!toggled);

  return (
    <>
      <Sidebar
        activeLink="/test"
        primaryColor="#e3d91c"
        secondaryColor="#e3d91c"
        getCurrentThemeColors={getBrandColors}
        sidebarConfig={sidebarConfig}
        toggleSidebar={toggleSidebar}
        collapsed
        toggled={toggled}
        appName="Test Component"
      />
      <Button onClick={() => toggleSidebar}>{collapsedButton}</Button>
    </>
  );
};

describe('Side Bar:', () => {
  const history = createMemoryHistory();

  const testComponentWithSidebar = (
    <BrowserRouter history={history}>
      <TestComponent />
    </BrowserRouter>
  );

  test('should be element with id = "pro-sidebar" ', () => {
    render(testComponentWithSidebar);

    expect(screen.getByTestId('pro-sidebar')).toBeInTheDocument();
  });

  test('should be "Sub Menu"', () => {
    render(testComponentWithSidebar);

    expect(screen.getByTestId('pro-sidebar-submenu')).toBeInTheDocument();
  });

  test('should on click opened Sub Menu', () => {
    render(testComponentWithSidebar);

    userEvent.click(screen.getByTestId('pro-sidebar-submenu'));

    expect(screen.getByText('Test List')).toBeInTheDocument();
  });

  test('should display navigation links', () => {
    render(testComponentWithSidebar);

    const nav = screen.getByRole('navigation');

    const navLinks = getAllByRole(nav, 'link');

    expect(navLinks).toHaveLength(sidebarConfig.length - 1);
  });

  test('should redirect to "Test List" after click current link', () => {
    const { getByText } = render(testComponentWithSidebar);

    userEvent.click(getByText('Test List'));

    waitFor(() => { expect(history.location.pathname).toBe('/list'); });
  });

  test('should be collapsed after click on button', () => {
    render(testComponentWithSidebar);

    const button = screen.getByText(collapsedButton);

    userEvent.click(button);

    waitFor(() => { expect(screen.getByText('Test')).not.toBeInTheDocument(); });
  });
});
