@import "~@nv2/nv2-pkg-js-theme/src/components/styles/variables.module";

$breakpoint-xl: 1024px;

.sidebar {
 height: calc(100vh - 60px);

  @media (max-width: $tablet-width) {
    top: 0;
    height: 100%;
  }

  &__header {
    display: none;
    justify-content: space-between;
    align-items: center;
    padding: 25px 20px;
    color: $white-color;

    @media (max-width: $tablet-width) {
      display: flex;
    }
  }

  &__close-btn {
    path {
      stroke: $white-color;
    }
  }

  &__nav-link-icon {
    path {
      color: $white-color;
      stroke: $white-color;
    }
  }

  &__link-name {
    font-weight: 700 !important;
    font-size: 14px !important;
    color: $white-color;
  }

  &-submenu {
    &__open-icon {
      font-size: 16px;

      & polyline {
        stroke: $white-color;
      }
    }

    &__link-name {
      font-size: 12px !important;
      padding-left: 12px !important;
    }
  }
}

.ps-sidebar .ps-menu {
  padding-top: 0;
  padding-bottom: 0;
}

.ps-menu-button {
  padding: 10px 13px 10px 5px !important;
}
