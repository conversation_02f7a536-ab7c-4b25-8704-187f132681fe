import React from 'react';
import PropTypes from 'prop-types';
import { Avatar } from '@mui/material';
import { withStyles } from '@mui/styles';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

const UserAvatar = (
  {
    userName,
    fontSize,
    lineHeight,
    size,
    cursor,
    describeId,
    onClick,
    getCurrentThemeColors,
    primaryColor,
  },
) => {
  const StyledAvatar = withStyles({
    root: {
      width: `${size}px`,
      height: `${size}px`,
      backgroundColor: `${getCurrentThemeColors(primaryColor)[50]}!important`,
      color: `${getCurrentThemeColors(primaryColor)[500]}!important`,
      fontSize: `${fontSize}px`,
      lineHeight: `${lineHeight}px`,
      fontWeight: '700',
      cursor,
    },
  })(Avatar);

  const getShortName = (name) => (
    name ? name.split(/\s+/)?.map((item) => item[0]?.toUpperCase())?.join('')?.slice(0, 2) : ''
  );

  return (
    <StyledAvatar
      aria-describedby={describeId}
      onClick={onClick}
    >
      {getShortName(userName)}
    </StyledAvatar>
  );
};

UserAvatar.propTypes = {
  userName: PropTypes.string.isRequired,
  size: PropTypes.number,
  fontSize: PropTypes.number,
  lineHeight: PropTypes.number,
  cursor: PropTypes.string,
  describeId: PropTypes.string,
  onClick: PropTypes.func,
  getCurrentThemeColors: PropTypes.func,
  primaryColor: PropTypes.string.isRequired,
};

UserAvatar.defaultProps = {
  size: 40,
  fontSize: 14,
  lineHeight: 19,
  cursor: 'auto',
  describeId: null,
  onClick: null,
  getCurrentThemeColors: (hex) => getBrandColors(hex),
};

export default UserAvatar;
