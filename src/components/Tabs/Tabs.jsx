import React from 'react';
import PropTypes from 'prop-types';
import MuiTabs from '@mui/material/Tabs';
import MuiTab from '@mui/material/Tab';
import { Box } from '@mui/material';
import TabComponent from './TabComponent';
import './Tabs.scss';

const Tabs = ({
  primaryColor, tabIndex, selectedNewTab, tabItemsConfig, setTabIndex,
}) => (
  <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
    <MuiTabs
      value={tabIndex}
      onChange={selectedNewTab}
      className="tabs"
      data-testid="tabs"
      sx={{ '.MuiTabs-indicator': { backgroundColor: `${primaryColor}!important` } }}
    >
      {tabItemsConfig.map((item, i) => {
        const selected = tabIndex === i;
        return (
          <MuiTab
            key={item.name}
            component={React.forwardRef((props, ref) => (
              <TabComponent
                icon={item.icon}
                title={item.name}
                dataComponent={item.dataComponent}
                onclick={() => setTabIndex(i)}
                selected={selected}
                disabled={item.disabled}
                primaryColor={primaryColor}
                {...ref}
                {...props}
              />
            ))}
          />
        );
      })}
    </MuiTabs>
  </Box>

);
Tabs.propTypes = {
  primaryColor: PropTypes.string.isRequired,
  tabIndex: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.bool,
  ]),
  selectedNewTab: PropTypes.func.isRequired,
  tabItemsConfig: PropTypes.instanceOf(Object).isRequired,
  setTabIndex: PropTypes.func,
};

Tabs.defaultProps = {
  setTabIndex: () => {},
  tabIndex: 0,
};

export default Tabs;
