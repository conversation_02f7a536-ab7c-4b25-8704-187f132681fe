import React from 'react';
import PropTypes from 'prop-types';
import { ButtonBase } from '@mui/material';
import { withStyles } from '@mui/styles';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';

const TabComponent = (props, ref) => {
  const {
    icon, title, dataComponent, onclick, disabled, selected, primaryColor, ...tabProps
  } = props;

  const StyledTab = withStyles({
    root: {
      borderBottom: '1px solid transparent !important',
      '&:hover': {
        color: `${primaryColor}!important`,
        borderBottom: `1px solid ${primaryColor} !important`,
        transition: 'all 0.8s',
        '& path': {
          stroke: primaryColor,
        },
        '& svg': {
          color: `${primaryColor}!important`,
        },
        '& .tabs-tab-component__title': {
          color: `${primaryColor}!important`,
        },
      },
      '&[aria-selected="true"]': {
        color: `${primaryColor}!important`,
        '& path': {
          stroke: primaryColor,
        },
        '& svg': {
          color: primaryColor,
        },
        '& .tabs-tab-component__title': {
          color: `${primaryColor}!important`,
        },
      },
      '&.Mui-disabled': {
        color: `${styles.darkColor200}!important`,
        '& path': {
          stroke: styles.darkColor200,
        },
        '& svg': {
          color: styles.darkColor200,
        },
        '& .tabs-tab-component__title': {
          color: styles.darkColor200,
        },
      },
    },
  })(ButtonBase);

  const newTabProps = {
    ...tabProps,
    children: (
      <>
        {icon}
        <span className="tabs-tab-component__title">{title}</span>
        {!disabled && dataComponent}
        {tabProps.children}
      </>),
  };

  return (
    <StyledTab
      role="tab"
      className="tabs-tab-component"
      onClick={onclick}
      disabled={disabled}
      aria-selected={selected}
      data-testid="tab-component"
      ref={{ ref }}
      {...newTabProps}
    />
  );
};

TabComponent.propTypes = {
  icon: PropTypes.instanceOf(Object),
  title: PropTypes.string,
  dataComponent: PropTypes.element,
  onclick: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  selected: PropTypes.bool.isRequired,
  primaryColor: PropTypes.string.isRequired,
};

TabComponent.defaultProps = {
  disabled: false,
  dataComponent: null,
  title: null,
  icon: null,

};
export default TabComponent;
