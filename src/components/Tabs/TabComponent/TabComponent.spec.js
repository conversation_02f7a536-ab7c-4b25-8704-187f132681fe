import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import { AiOutlineSliders } from 'react-icons/ai';
import TabComponent from './TabComponent';

const primaryColor = '#1912D2';
const testData = 0;

describe('Tabs: TabComponent', () => {
  const mockProps = {
    icon: <AiOutlineSliders data-testid="tab-icon" />,
    title: 'Test Budget Item',
    dataComponent: <span>{testData}</span>,
    onclick: jest.fn(),
    selected: true,
    primaryColor,
  };
  const tabComponent = (
    <TabComponent {...mockProps} />
  );

  test('should be "Tab Component" in the DOM ', () => {
    const { getByTestId } = render(tabComponent);
    expect(getByTestId('tab-component')).toBeInTheDocument();
  });

  test('should be "Title" in the DOM ', () => {
    const { getByText } = render(tabComponent);
    expect(getByText(mockProps.title)).toBeInTheDocument();
  });

  test('should be primary color of "Title" when it selected ', () => {
    const { getByText } = render(tabComponent);
    expect(getByText(mockProps.title)).toHaveStyle({ color: primaryColor });
  });

  test('should be Tab Icon in the DOM ', () => {
    const { getByTestId } = render(tabComponent);
    expect(getByTestId('tab-icon')).toBeInTheDocument();
  });

  test('should be call onClick function  ', () => {
    const { getByTestId } = render(tabComponent);
    const testTab = getByTestId('tab-component');
    fireEvent.click(testTab);
    expect(mockProps.onclick).toBeCalled();
  });

  test('should be Tab data Component in the DOM ', () => {
    const { getByText } = render(tabComponent);
    expect(getByText(testData)).toBeInTheDocument();
  });
});
