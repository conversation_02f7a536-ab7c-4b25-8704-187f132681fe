import React from 'react';
import { AiOutlineSetting, AiOutlineUsergroupAdd } from 'react-icons/ai';
import { fireEvent, render, waitFor } from '@testing-library/react';
import Tabs from './Tabs';

const primaryColor = '#1912D2';
const testName = 'Settings';

const tabItemsConfig = [
  {
    name: 'Clients',
    icon: <AiOutlineUsergroupAdd size={21} />,
  },
  {
    name: testName,
    icon: <AiOutlineSetting size={21} />,
    dataComponent: <span className="test-data">0</span>,
    disabled: true,

  },
];
const mockProps = {
  primaryColor,
  tabIndex: 0,
  selectedNewTab: jest.fn(),
  tabItemsConfig,
  setTabIndex: jest.fn(),
};

describe('Tabs', () => {
  test('should be Tabs in the DOM ', () => {
    const { getByTestId } = render(<Tabs {...mockProps} />);
    expect(getByTestId('tabs')).toBeInTheDocument();
  });

  test('should be Tabs Indicator in the DOM', () => {
    render(<Tabs {...mockProps} />);

    const indicator = document.querySelector('.MuiTabs-indicator');
    expect(indicator).toBeInTheDocument();
  });

  test('should be called function selectedNewTab', () => {
    const { getByText } = render(<Tabs {...mockProps} />);
    const selectTab = getByText(testName);
    fireEvent.click(selectTab);

    waitFor(() => { expect(mockProps.selectedNewTab).toBeCalled(); });
  });
});
