import { useEffect, useCallback } from 'react';
import TagManager from 'react-gtm-module';

const useGTM = (gtmId, options = {}) => {
  const { onError } = options;

  useEffect(() => {
    if (!gtmId || typeof window === 'undefined') {
      return;
    }

    if (window.__GTM_INITIALIZED_ID__ === gtmId) {
      return;
    }

    if (window.googleTagManager
        && window.__GTM_INITIALIZED_ID__
        && window.__GTM_INITIALIZED_ID__ !== gtmId) {
      return;
    }

    try {
      TagManager.initialize({ gtmId });
      window.__GTM_INITIALIZED_ID__ = gtmId;
    } catch (error) {
      const errorMessage = error instanceof Error ? error : new Error('GTM initialization failed');
      if (onError) {
        onError(errorMessage, 'initialization');
      }
    }
  }, [gtmId, onError]);

  const pushToDataLayer = useCallback((data) => {
    if (typeof window === 'undefined') {
      return;
    }

    try {
      TagManager.dataLayer({ dataLayer: data });
    } catch (error) {
      const errorMessage = error instanceof Error ? error : new Error('DataLayer push failed');
      if (onError) {
        onError(errorMessage, 'dataLayerPush');
      }
    }
  }, [onError]);

  const trackEvent = useCallback((eventName, eventData = {}) => {
    pushToDataLayer({
      event: eventName,
      ...eventData,
    });
  }, [pushToDataLayer]);

  const trackPageView = useCallback((pageData = {}) => {
    trackEvent('pageview', {
      page_path: pageData.page || window.location.pathname,
      page_title: pageData.title || document.title,
      ...pageData,
    });
  }, [trackEvent]);

  const trackClick = useCallback((clickData = {}) => {
    trackEvent('click', {
      event_category: 'engagement',
      ...clickData,
    });
  }, [trackEvent]);

  const isReady = useCallback(() => (
    typeof window !== 'undefined'
    && !!window.googleTagManager
    && !!window.__GTM_INITIALIZED_ID__
  ), []);

  return {
    trackEvent,
    trackPageView,
    trackClick,
    isReady,
    pushToDataLayer,
  };
};

export default useGTM;
