export interface GTMOptions {
  onError?: (error: Error, context: string) => void;
}

export interface GTMWrapperProps extends GTMOptions {
  gtmId: string;
}

export interface GTMHookReturn {
  trackEvent: (eventName: string, eventData?: Record<string, any>) => void;
  trackPageView: (pageData?: { page?: string; title?: string; [key: string]: any }) => void;
  trackClick: (clickData?: Record<string, any>) => void;
  isReady: () => boolean;
  pushToDataLayer: (data: Record<string, any>) => void;
}

export interface GTMProviderProps {
  children: React.ReactNode;
  gtmId?: string;
  onError?: (error: Error, context: string) => void;
}
