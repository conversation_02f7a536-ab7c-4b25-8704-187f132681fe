import React from 'react';
import { render } from '@testing-library/react';
import GTMWrapper from './GTMWrapper';
import useGTM from './useGTM';

jest.mock('./useGTM', () => ({
  __esModule: true,
  default: jest.fn(),
}));

describe('GTMWrapper Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should call useGTM hook with correct gtmId', () => {
    render(<GTMWrapper gtmId="GTM-TEST123" />);
    expect(useGTM).toHaveBeenCalledWith('GTM-TEST123', {
      onError: undefined,
    });
  });

  test('should call useGTM hook with onError option', () => {
    const onError = jest.fn();
    render(
      <GTMWrapper
        gtmId="GTM-TEST123"
        onError={onError}
      />,
    );
    expect(useGTM).toHaveBeenCalledWith('GTM-TEST123', {
      onError,
    });
  });

  test('should render without crashing', () => {
    expect(() => {
      render(<GTMWrapper gtmId="GTM-TEST123" />);
    }).not.toThrow();
  });

  test('should render null (no visible output)', () => {
    const { container } = render(<GTMWrapper gtmId="GTM-TEST123" />);
    expect(container.firstChild).toBeNull();
  });

  test('should render without crashing when useGTM throws', () => {
    useGTM.mockImplementationOnce(() => {
      throw new Error('useGTM failed');
    });

    expect(() => {
      render(<GTMWrapper gtmId="GTM-TEST123" />);
    }).not.toThrow();
  });

  test('should re-initialize GTM when gtmId changes', () => {
    const { rerender } = render(<GTMWrapper gtmId="GTM-TEST123" />);
    expect(useGTM).toHaveBeenCalledWith('GTM-TEST123', {
      onError: undefined,
    });

    rerender(<GTMWrapper gtmId="GTM-TEST456" />);
    expect(useGTM).toHaveBeenCalledWith('GTM-TEST456', {
      onError: undefined,
    });
  });
});
