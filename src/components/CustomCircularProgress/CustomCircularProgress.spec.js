import React from 'react';
import { render, screen } from '@testing-library/react';
import CustomCircularProgress from './CustomCircularProgress';

describe('CustomCircularProgress:', () => {
  test('should be CustomCircularProgress in the DOM', () => {
    render(<CustomCircularProgress primaryColor="#ff0000" />);

    const element = screen.getByTestId('circular-progress');

    expect(element).toBeInTheDocument();
  });

  test('should be  custom size CustomCircularProgress', () => {
    const customSize = 40;

    render(<CustomCircularProgress primaryColor="#ff0000" size={customSize} isBrandBackground />);

    const element = screen.getByTestId('circular-progress');

    expect(element).toHaveStyle(`width: ${customSize}px`);
  });
});
