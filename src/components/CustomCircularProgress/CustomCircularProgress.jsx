import React from 'react';
import { Box, CircularProgress, circularProgressClasses } from '@mui/material';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import PropTypes from 'prop-types';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

const CustomCircularProgress = (props) => {
  const {
    isBrandBackground,
    primaryColor,
    size,
    thickness,
    className,
    getCurrentThemeColors,
    ...otherProps
  } = props;

  const progressBackgroundColor = isBrandBackground
    ? getCurrentThemeColors(primaryColor)[50]
    : styles.whiteColor;

  return (
    <div className={className} style={{ display: 'flex', justifyContent: 'center' }}>
      <Box sx={{ position: 'relative' }}>
        <CircularProgress
          variant="determinate"
          sx={{
            color: progressBackgroundColor,
          }}
          size={size}
          thickness={thickness}
          value={100}
          {...otherProps}
        />
        <CircularProgress
          data-testid="circular-progress"
          variant="indeterminate"
          disableShrink
          sx={{
            animationDuration: '1000ms',
            color: primaryColor,
            position: 'absolute',
            left: 0,
            [`& .${circularProgressClasses.circle}`]: {
              strokeLinecap: 'round',
            },
          }}
          size={size}
          thickness={thickness}
          {...otherProps}
        />
      </Box>
    </div>
  );
};

CustomCircularProgress.propTypes = {
  isBrandBackground: PropTypes.bool,
  primaryColor: PropTypes.string.isRequired,
  size: PropTypes.number,
  thickness: PropTypes.number,
  className: PropTypes.string,
  getCurrentThemeColors: PropTypes.func,
};
CustomCircularProgress.defaultProps = {
  isBrandBackground: true,
  size: 23,
  thickness: 2,
  className: '',
  getCurrentThemeColors: (hex) => getBrandColors(hex),
};
export default CustomCircularProgress;
