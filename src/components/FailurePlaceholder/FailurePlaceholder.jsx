import React from 'react';
import { AiOutlineExclamationCircle } from 'react-icons/ai';
import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';
import { Typography } from '@mui/material';
import PropTypes from 'prop-types';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

const FailurePlaceholder = ({
  errorDescription, className, iconSize, getCurrentThemeColors, themeName, icon,
}) => {
  const { redColor } = themeConfig[themeName];

  return (
    <div className={`error-placeholder ${className}`}>
      <div
        className="error-placeholder__icon-wrap"
        style={{ backgroundColor: getCurrentThemeColors(redColor)[50] }}
        data-testid="error-placeholder"
      >
        {icon || <AiOutlineExclamationCircle fill={redColor} size={iconSize} />}
      </div>
      {errorDescription && (
      <Typography variant="body1" className="error-placeholder__text">
        {errorDescription}
      </Typography>
      )}
    </div>
  );
};

FailurePlaceholder.propTypes = {
  errorDescription: PropTypes.string,
  className: PropTypes.string,
  iconSize: PropTypes.number,
  getCurrentThemeColors: PropTypes.func,
  themeName: PropTypes.string,
  icon: PropTypes.element,
};

FailurePlaceholder.defaultProps = {
  errorDescription: null,
  className: '',
  iconSize: 31,
  getCurrentThemeColors: (hex) => getBrandColors(hex),
  themeName: 'bt',
  icon: null,
};

export default FailurePlaceholder;
