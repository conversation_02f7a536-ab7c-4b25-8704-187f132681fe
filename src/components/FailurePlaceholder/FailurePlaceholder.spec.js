import React from 'react';
import { render, screen } from '@testing-library/react';
import { AiOutlineWarning } from 'react-icons/ai';
import FailurePlaceholder from './FailurePlaceholder';

describe('FailurePlaceholder', () => {
  const errorDescription = 'errorDescription';

  const failurePlaceholder = (
    <FailurePlaceholder errorDescription={errorDescription} />
  );

  test('should be Failure Placeholder in the DOM', () => {
    render(failurePlaceholder);

    expect(screen.getByTestId('error-placeholder')).toBeInTheDocument();
  });

  test('should be  error description in Failure Placeholder', () => {
    render(failurePlaceholder);

    expect(screen.getByText(errorDescription)).toBeInTheDocument();
  });

  test('should render with custom icon when provided', () => {
    const customIcon = <AiOutlineWarning data-testid="custom-icon" />;

    render(
      <FailurePlaceholder
        errorDescription={errorDescription}
        icon={customIcon}
      />,
    );

    expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
  });
});
