import React from 'react';

import PropTypes from 'prop-types';
import { Tooltip, Typography } from '@mui/material';
import './LimitedRowWithShowMore.scss';

const LimitedRowWithShowMore = ({
  row, limit, tooltipWidth, tooltipComponent, showTooltip,
}) => {
  const comma = ',';
  const array = row.split(comma).map((item) => item.trim());

  if (array.length <= limit && !tooltipComponent && showTooltip) {
    return row;
  }

  const firstLimitedItems = array.slice(0, limit).join(', ');

  const notShownItemsAmount = array.length - limit;

  const moreItemsText = notShownItemsAmount > 0 ? ` and ${notShownItemsAmount} more` : '';

  const limitedRowWithShowMore = `${firstLimitedItems}${moreItemsText}`;

  if (showTooltip) {
    return (
      <Tooltip
        title={tooltipComponent || row}
        placement="top"
        arrow
        PopperProps={{
          style: { maxWidth: tooltipWidth },
        }}
      >
        <span>{limitedRowWithShowMore}</span>
      </Tooltip>
    );
  }

  return (
    <div className="limited-row">
      <Typography variant="body2" component="p" className="limited-row__text-bold">
        {firstLimitedItems}
      </Typography>
      {notShownItemsAmount > 0 && (
        <Typography variant="body2" component="p" className="limited-row__text-normal">
          and
          <Typography component="span" className="limited-row__text-bold">
            {' '}
            {notShownItemsAmount}
          </Typography>
          {' '}
          more
        </Typography>
      )}
    </div>
  );
};

LimitedRowWithShowMore.propTypes = {
  row: PropTypes.string.isRequired,
  limit: PropTypes.number,
  showTooltip: PropTypes.bool,
  tooltipWidth: PropTypes.string,
  tooltipComponent: PropTypes.element,
};

LimitedRowWithShowMore.defaultProps = {
  limit: 2,
  showTooltip: true,
  tooltipWidth: 'auto',
  tooltipComponent: null,
};

export default LimitedRowWithShowMore;
