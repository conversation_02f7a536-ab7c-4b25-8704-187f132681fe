import { toastr } from 'react-redux-toastr';

import {
  errorToastrOptions,
  successToastrOptions,
  infoToastrOptions,
} from './constants';

export const showErrorToastr = (message, options = {}) => () => {
  const toastrOptions = {
    ...errorToastrOptions,
    ...options,
  };

  toastr.error(message, toastrOptions);
};

export const showSuccessToastr = (message, options = {}) => () => {
  const toastrOptions = {
    ...successToastrOptions,
    ...options,
  };
  toastr.info(message, toastrOptions);
};

export const showInfoToastr = (message, options = {}) => () => {
  const toastrOptions = {
    ...infoToastrOptions,
    ...options,
  };
  toastr.info(message, toastrOptions);
};
