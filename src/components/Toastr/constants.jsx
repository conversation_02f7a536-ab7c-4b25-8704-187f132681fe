import React from 'react';

import {
  AiOutlineExclamationCircle,
  AiOutlineCheckCircle,
  AiOutlineInfoCircle,
} from 'react-icons/ai';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';

const defaultToastrOptions = {
  timeOut: 4000,
  closeOnToastrClick: true,
  transitionIn: 'fadeIn',
  transitionOut: 'fadeOut',
};

export const errorToastrOptions = {
  icon: (<AiOutlineExclamationCircle className="toastr-icon" color={styles.redColor400} size={24} />),
  className: 'error-toastr',
  ...defaultToastrOptions,
};

export const successToastrOptions = {
  icon: (<AiOutlineCheckCircle className="toastr-icon" color={styles.greenColor500} size={24} />),
  className: 'success-toastr',
  ...defaultToastrOptions,
};

export const infoToastrOptions = {
  icon: (<AiOutlineInfoCircle className="toastr-icon" color={styles.brandBlueColor500} size={24} />),
  className: 'info-toastr',
  ...defaultToastrOptions,
};
