$brand-blue-color-50: #DFDEF4;

.error-toastr {
  border-left: 3px solid $red-color-400;
  background-color: $red-color-100;
}

.success-toastr {
  border-left: 3px solid $green-color-500;
  background-color: $green-color-100;
}

.info-toastr {
  border-left: 3px solid $brand-blue-color-500;
  background-color: $brand-blue-color-50;
}

.error-toastr, .success-toastr, .info-toastr {
  z-index: 1200;
  box-shadow: $shadow8;
  padding: 20px 16px;
  position: fixed;
  bottom: 30px;
  right: 30px;
  border-radius: 4px;
  @media (max-width: $mobile-width) {
    width: calc(100% - 20px);
    bottom: 10px;
    right: 10px
  }
  div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
  }
  img {
    margin-right: 15px;
  }
  .toastr-icon {
    margin-right: 18px;
  }
}
.close-toastr {
  span {
    display: flex;
    width: 15px;
    height: 15px;
    cursor: pointer;
    margin-left: 15px;
    align-items: center;
  }
}
.fadeIn {
  animation-name: fadeIn;
  animation-duration: .7s
}
.fadeOut {
  animation-name: fadeOut;
  animation-duration: .3s
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}
@keyframes fadeOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}
