import { act, renderHook } from '@testing-library/react';
import { MuiTableProvider, useMuiTableContext } from './MuiTableContext';

describe('useMuiTableContext', () => {
  test('should initialize with default values', () => {
    const { result } = renderHook(() => useMuiTableContext(), {
      wrapper: MuiTableProvider,
    });

    expect(result.current.searchValue).toBe('');
    expect(result.current.pagination).toEqual({ page: 1, pageSize: 10 });
    expect(result.current.sort).toEqual([]);
  });

  test('should update search value', () => {
    const { result } = renderHook(() => useMuiTableContext(), {
      wrapper: MuiTableProvider,
    });

    act(() => {
      result.current.setSearchValue('test');
    });

    expect(result.current.searchValue).toBe('test');
  });

  test('should update pagination', () => {
    const { result } = renderHook(() => useMuiTableContext(), {
      wrapper: MuiTableProvider,
    });

    act(() => {
      result.current.setPagination({ page: 2, pageSize: 20 });
    });

    expect(result.current.pagination).toEqual({ page: 2, pageSize: 20 });
  });

  test('should update sort', () => {
    const { result } = renderHook(() => useMuiTableContext(), {
      wrapper: MuiTableProvider,
    });

    act(() => {
      result.current.setSort([{ field: 'name', sort: 'asc' }]);
    });

    expect(result.current.sort).toEqual([{ field: 'name', sort: 'asc' }]);
  });

  test('should trigger pagination change', () => {
    const { result } = renderHook(() => useMuiTableContext(), {
      wrapper: MuiTableProvider,
    });

    act(() => {
      result.current.onPaginationModelChange({ page: 3, pageSize: 30 });
    });

    expect(result.current.pagination).toEqual({ page: 3, pageSize: 30 });
  });

  test('should trigger sort change', () => {
    const { result } = renderHook(() => useMuiTableContext(), {
      wrapper: MuiTableProvider,
    });

    act(() => {
      result.current.onSortModelChange([{ field: 'age', sort: 'desc' }]);
    });

    expect(result.current.sort).toEqual([{ field: 'age', sort: 'desc' }]);
  });

  test('should set page to 1 as sort is changed', () => {
    const { result } = renderHook(() => useMuiTableContext(), {
      wrapper: MuiTableProvider,
    });

    act(() => {
      result.current.setPagination({ page: 2, pageSize: 10 });
    });

    act(() => {
      result.current.onSortModelChange([{ field: 'age', sort: 'desc' }]);
    });

    expect(result.current.pagination.page).toEqual(1);
  });

  test('should set page to 1 as search is changed', () => {
    const { result } = renderHook(() => useMuiTableContext(), {
      wrapper: MuiTableProvider,
    });

    act(() => {
      result.current.setPagination({ page: 2, pageSize: 10 });
    });

    act(() => {
      result.current.onSearchValueChange('test');
    });

    expect(result.current.pagination.page).toEqual(1);
  });
});
