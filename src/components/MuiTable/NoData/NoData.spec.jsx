import React from 'react';
import { render, screen } from '@testing-library/react';
import NoData from './NoData';

describe('NoData', () => {
  const mockIcon = <span data-testid="mock-icon">Icon</span>;
  const mockTitle = 'Mock Title';
  const mockDescription = 'Mock Description';
  const mockPrimaryColor = '#ff0000';
  const mockGetBrandColors = () => ({ 50: '#ff0000' });

  it('renders the default props correctly', () => {
    render(
      <NoData
        primaryColor={mockPrimaryColor}
        getCurrentThemeColors={mockGetBrandColors}
      />,
    );

    expect(screen.getByText('No data')).toBeInTheDocument();
    expect(screen.getByText('No data to display')).toBeInTheDocument();
  });

  test('renders component with provided props', () => {
    render(
      <NoData
        icon={mockIcon}
        title={mockTitle}
        description={mockDescription}
        primaryColor={mockPrimaryColor}
        getCurrentThemeColors={mockGetBrandColors}
      />,
    );

    const iconElement = screen.getByTestId('mock-icon');
    const titleElement = screen.getByText(mockTitle);
    const descriptionElement = screen.getByText(mockDescription);

    expect(iconElement).toBeInTheDocument();
    expect(titleElement).toBeInTheDocument();
    expect(descriptionElement).toBeInTheDocument();
  });

  test('null for additionalItem should render', () => {
    render(
      <NoData
        icon={mockIcon}
        title={mockTitle}
        description={mockDescription}
        additionalItem={null}
        primaryColor={mockPrimaryColor}
        getCurrentThemeColors={mockGetBrandColors}
      />,
    );

    const iconElement = screen.getByTestId('mock-icon');
    const titleElement = screen.getByText(mockTitle);
    const descriptionElement = screen.getByText(mockDescription);

    expect(iconElement).toBeInTheDocument();
    expect(titleElement).toBeInTheDocument();
    expect(descriptionElement).toBeInTheDocument();
  });

  test('button for additionalItem should render', () => {
    render(
      <NoData
        icon={mockIcon}
        title={mockTitle}
        description={mockDescription}
        additionalItem={(
          <button
            type="button"
            className="table__no-data-btn"
            data-testid="mock-button"
          >
            Click here
          </button>
        )}
        primaryColor={mockPrimaryColor}
        getCurrentThemeColors={mockGetBrandColors}
      />,
    );

    const iconElement = screen.getByTestId('mock-icon');
    const buttonMock = screen.getByTestId('mock-button');
    const titleElement = screen.getByText(mockTitle);
    const descriptionElement = screen.getByText(mockDescription);

    expect(iconElement).toBeInTheDocument();
    expect(titleElement).toBeInTheDocument();
    expect(descriptionElement).toBeInTheDocument();
    expect(buttonMock).toBeInTheDocument();
  });
});
