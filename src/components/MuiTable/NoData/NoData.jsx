import React from 'react';
import PropTypes from 'prop-types';
import { Typography } from '@mui/material';
import { AiOutlineLineChart } from 'react-icons/ai';

import './NoData.scss';

const NoData = ({
  icon, title, description, additionalItem, primaryColor, getCurrentThemeColors,
}) => (
  <div className="table__no-data-wrap">
    <div
      className="table__no-data-icon-wrap"
      style={{ backgroundColor: getCurrentThemeColors(primaryColor)[50] }}
    >
      {icon || <AiOutlineLineChart size={24} color={primaryColor} />}
    </div>

    <Typography variant="h3" component="h3" className="table__no-data-title">
      {title}
    </Typography>
    <Typography variant="body1" className="table__no-data-text">
      {description}
    </Typography>
    {additionalItem}
  </div>
);

NoData.propTypes = {
  icon: PropTypes.element,
  title: PropTypes.string,
  additionalItem: PropTypes.instanceOf(Object),
  description: PropTypes.string,
  primaryColor: PropTypes.string.isRequired,
  getCurrentThemeColors: PropTypes.func.isRequired,
};

NoData.defaultProps = {
  icon: null,
  title: 'No data',
  additionalItem: null,
  description: 'No data to display',
};

export default NoData;
