import React from 'react';
import {
  fireEvent, render, waitFor, act, screen,
} from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import MuiTable from './MuiTable';
import { MuiTableProvider } from './MuiTableContext';
import { firstPage } from './constants';
import { mockColumns, mockRows } from './mock';

jest.mock('@mui/x-data-grid', () => {
  const { DataGrid } = jest.requireActual('@mui/x-data-grid');
  return {
    ...jest.requireActual('@mui/x-data-grid'),
    DataGrid: (props) => <DataGrid {...props} disableVirtualization />,
  };
});

function getCell(rowIndex, colIndex) {
  const cell = document.querySelector(
    `[role="row"][data-rowindex="${rowIndex}"] [role="cell"][data-colindex="${colIndex}"]`,
  );
  if (cell == null) {
    throw new Error(`Cell ${rowIndex} ${colIndex} not found`);
  }
  return cell;
}

function ResizeObserverMock(callback) {
  let timeout;

  return {
    observe: (element) => {
      // Simulates the async behavior of the native ResizeObserver
      timeout = setTimeout(() => {
        callback([{ borderBoxSize: [{ blockSize: element.clientHeight }] }]);
      });
    },
    disconnect: () => {
      clearTimeout(timeout);
    },
    unobserve: () => {},
  };
}

const originalResizeObserver = window.ResizeObserver;

const onClickButton = (getter, testId) => {
  const button = getter(testId);
  fireEvent.click(button);
};

describe('MuiTable', () => {
  const mockOnChangePagination = jest.fn();
  const renderMuiTable = (props, contextValue, fixedColumns = {}) => render(
    <MuiTableProvider {...contextValue}>
      <MuiTable {...props} fixedColumns={fixedColumns} />
    </MuiTableProvider>,
  );

  const defaultProps = {
    columns: mockColumns,
    rows: mockRows,
    rowCount: mockRows.length,
    showFirstLastPageButtons: true,
    maxTableHeight: '300px',
  };

  const defaultContextValue = {
    onChange: mockOnChangePagination,
  };

  const defaultPagination = { page: 3, pageSize: 20 };
  beforeEach(() => {
    window.ResizeObserver = ResizeObserverMock;
  });

  afterEach(() => {
    jest.clearAllMocks();
    window.ResizeObserver = originalResizeObserver;
  });

  test('should renders MuiTable without crashing', () => {
    const { getByTestId } = renderMuiTable(defaultProps, defaultContextValue);

    expect(getByTestId('mui-table')).toBeInTheDocument();
  });

  test('should render Table Data', async () => {
    await act(() => renderMuiTable(defaultProps, defaultContextValue));

    await waitFor(() => {
      const firstColumnHeader = screen.getAllByRole('columnheader')[1];
      const firstCellInFirstColumn = getCell(0, 1);

      expect(firstColumnHeader.textContent).toBe(mockColumns[1].headerName);
      expect(firstCellInFirstColumn.textContent).toBe(mockRows[0].lastLogin);
    });
  });

  test('should render Table without fixed columns', async () => {
    await act(() => renderMuiTable(defaultProps, defaultContextValue));

    await waitFor(() => {
      const firstColumnHeader = screen.getAllByRole('columnheader')[0];
      const firstCellInFirstColumn = getCell(0, 0);

      expect(firstColumnHeader).toHaveStyle('position: relative');
      expect(firstCellInFirstColumn).not.toHaveStyle('position: sticky');
    });
  });

  test('should renders fixed columns ', async () => {
    const fixedColumns = { 0: 50 };
    await act(() => renderMuiTable(defaultProps, defaultContextValue, fixedColumns));

    await waitFor(() => {
      const firstColumnHeader = screen.getAllByRole('columnheader')[0];
      const firstCellInFirstColumn = getCell(0, 0);

      expect(firstColumnHeader).toHaveStyle('position: sticky');
      expect(firstCellInFirstColumn).toHaveStyle('position: sticky');
    });
  });

  test('should call onChangePagination when pagination changes', async () => {
    const { getByTestId } = renderMuiTable(defaultProps, defaultContextValue);

    onClickButton(getByTestId, 'next-page');

    expect(mockOnChangePagination).toHaveBeenCalledTimes(1);
  });

  test('should prev button be disabled', () => {
    const { getByTestId } = renderMuiTable(defaultProps, defaultContextValue);
    const prevPageButton = getByTestId('previous-page');

    expect(prevPageButton).toBeDisabled();
  });

  test('should not show pagination when isPaginationDisabled is true', () => {
    const { queryByTestId } = renderMuiTable(
      { ...defaultProps, isPaginationDisabled: true },
      defaultContextValue,
    );

    expect(queryByTestId('pagination')).not.toBeInTheDocument();
  });

  test('should show pagination when isPaginationDisabled is false', () => {
    const { getByTestId } = renderMuiTable(
      { ...defaultProps, isPaginationDisabled: false },
      defaultContextValue,
    );

    expect(getByTestId('pagination')).toBeInTheDocument();
  });

  describe('actions', () => {
    test('should renders render actions', () => {
      const actionButtonText = 'action button';
      const Actions = () => <button onClick={jest.fn()} type="button">{actionButtonText}</button>;

      const { getByText } = renderMuiTable({ ...defaultProps, Actions });

      expect(getByText(actionButtonText)).toBeInTheDocument();
    });

    test('should renders render actions on the left side of the interactions area', () => {
      const actionButtonText = 'action button left';
      const ActionsLeft = () => <button onClick={jest.fn()} type="button">{actionButtonText}</button>;

      const { getByText } = renderMuiTable({ ...defaultProps, ActionsLeft });

      expect(getByText(actionButtonText)).toBeInTheDocument();
    });
  });

  describe('pagination', () => {
    const renderToTestPagination = (withDefaultPagination = true) => renderMuiTable({
      ...defaultProps,
      rowCount: 100,
    }, {
      ...defaultContextValue,
      defaultPagination: withDefaultPagination ? defaultPagination : undefined,
    });

    const paginationCalledWith = async (pagination = {}, sort = {}, details) => {
      await waitFor(() => {
        expect(mockOnChangePagination).toBeCalledWith(pagination, sort, details);
      });
    };

    test('should call onChangePagination when pagination is changed to next page', () => {
      const { getByTestId } = renderToTestPagination(false);
      onClickButton(getByTestId, 'next-page');
      paginationCalledWith({ page: 2, pageSize: 10 });
    });

    test('should call onChangePagination when pagination is changed to previous page', () => {
      const { getByTestId } = renderToTestPagination();
      onClickButton(getByTestId, 'previous-page');
      paginationCalledWith({ page: 2, pageSize: 20 });
    });

    test('should set default pagination', () => {
      const { getByTestId } = renderToTestPagination();
      onClickButton(getByTestId, 'next-page');
      paginationCalledWith({ page: 4, pageSize: 20 });
    });

    test('should paginate to first page', () => {
      const { getByTestId } = renderToTestPagination();
      onClickButton(getByTestId, 'first-page');
      paginationCalledWith({ page: firstPage, pageSize: 20 });
    });

    test('should paginate to last page', () => {
      const { getByTestId } = renderToTestPagination();
      onClickButton(getByTestId, 'last-page');
      paginationCalledWith({ page: 5, pageSize: 20 });
    });

    test('should render 1, 2, 3 buttons by default', () => {
      const { getAllByText } = renderToTestPagination(false);
      const button1 = getAllByText('1');
      const button2 = getAllByText('2');
      const button3 = getAllByText('3');

      expect(button1[0]).toBeInTheDocument();
      expect(button2[0]).toBeInTheDocument();
      expect(button3[0]).toBeInTheDocument();
    });

    test('should render right numeric buttons if hase pagination state', () => {
      const { getAllByText } = renderToTestPagination();
      screen.debug();
      const button2 = getAllByText((defaultPagination.page - 1).toString());
      const button1 = getAllByText(defaultPagination.page.toString());
      const button3 = getAllByText((defaultPagination.page + 1).toString());

      expect(button1[0]).toBeInTheDocument();
      expect(button2[0]).toBeInTheDocument();
      expect(button3[0]).toBeInTheDocument();
    });
  });
});
