import React, { useCallback } from 'react';
import { debounce } from 'lodash';
import {
  bool, element, func, instanceOf, oneOfType, string,
} from 'prop-types';
import SearchInput from './SearchInput';
import SelectionsInfo from './SelectionsInfo';

const Interactions = (props) => {
  const {
    searchValue, pagination, sort, isVisibleSearchInput, onSearchValueChange,
    setSearchValue, Actions, ActionsLeft, checkboxSelection, rowSelectionModel, rows,
  } = props;

  const debounceSearch = useCallback(debounce(onSearchValueChange, 500), [onSearchValueChange]);

  const handleSearchValueChange = (value) => {
    setSearchValue(value);
    debounceSearch(value);
  };

  if (!isVisibleSearchInput && !Actions && !ActionsLeft && !checkboxSelection) {
    return null;
  }

  return (
    <div className="interactions">
      <div>
        {ActionsLeft && (
          <ActionsLeft
            {...props}
            pagination={pagination}
            sort={sort}
            searchValue={searchValue}
          />
        )}
      </div>
      <div>
        {isVisibleSearchInput && (
          <SearchInput
            value={searchValue}
            placeholder="Search"
            onChange={handleSearchValueChange}
          />
        )}
      </div>
      <div className="interactions__actions">
        <SelectionsInfo
          checkboxSelection={checkboxSelection}
          rowSelectionModel={rowSelectionModel}
          rows={rows}
        />
        {Actions && (
          <Actions
            {...props}
            pagination={pagination}
            sort={sort}
            searchValue={searchValue}
          />
        )}
      </div>
    </div>
  );
};

Interactions.propTypes = {
  searchValue: string.isRequired,
  pagination: instanceOf(Object).isRequired,
  sort: instanceOf(Array).isRequired,
  isVisibleSearchInput: bool.isRequired,
  onSearchValueChange: func.isRequired,
  setSearchValue: func.isRequired,
  Actions: oneOfType([element, func]),
  ActionsLeft: oneOfType([element, func]),
  checkboxSelection: bool.isRequired,
  rowSelectionModel: instanceOf(Array).isRequired,
  rows: instanceOf(Array).isRequired,
};

Interactions.defaultProps = {
  Actions: null,
  ActionsLeft: null,
};

export default Interactions;
