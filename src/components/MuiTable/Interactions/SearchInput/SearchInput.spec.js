import React from 'react';
import { screen, waitFor, render } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import SearchInput from './SearchInput';

const tooltipText = 'Tooltip';

describe('SearchInput', () => {
  test('should SearchInput correct render', () => {
    render(<SearchInput placeholder="Search" value="" onChange={() => true} />);

    expect(screen.getByPlaceholderText('Search')).toBeInTheDocument();
  });

  test('should render tooltip on hover', () => {
    const { getByTestId } = render(<SearchInput placeholder="Search" value="" onChange={() => true} tooltip={tooltipText} />);

    const wrapper = getByTestId('search-input');

    userEvent.hover(wrapper);

    waitFor(() => {
      expect(getByTestId('search-tooltip')).toBeInTheDocument();
    });
  });

  test('should render tooltip with text "Tooltip"', () => {
    const { getByTestId, getByText } = render(<SearchInput placeholder="Search" value="" onChange={() => true} tooltip={tooltipText} />);

    const wrapper = getByTestId('search-input');

    userEvent.hover(wrapper);

    waitFor(() => {
      expect(getByText(tooltipText)).toBeInTheDocument();
    });
  });
});
