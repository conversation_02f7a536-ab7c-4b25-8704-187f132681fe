import React from 'react';
import { AiOutlineSearch } from 'react-icons/ai';
import {
  TextField, styled, Tooltip, InputAdornment,
} from '@mui/material';
import PropTypes from 'prop-types';

import './SearchInput.scss';

const StyledTextField = styled(TextField)(({ theme }) => ({
  color: 'inherit',
  width: '100%',
  height: '40px',
  '& .MuiInputBase-input': {
    padding: theme.spacing(1, 1, 1, 0),
    fontSize: '14px',
    paddingLeft: theme.spacing(2),
    width: '100%',
  },
}));

const SearchInput = ({
  placeholder, value, onChange, tooltip,
}) => (
  <div className="search" data-testid="search-input">
    <Tooltip title={tooltip} arrow placement="bottom" data-testid="search-tooltip">
      <StyledTextField
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        InputProps={{
          'aria-label': 'search',
          startAdornment: (
            <InputAdornment position="start">
              <AiOutlineSearch size={21} />
            </InputAdornment>
          ),
        }}
      />
    </Tooltip>
  </div>
);

SearchInput.propTypes = {
  placeholder: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func,
  tooltip: PropTypes.string,
};

SearchInput.defaultProps = {
  placeholder: '',
  value: '',
  onChange: () => {},
  tooltip: '',
};

export default SearchInput;
