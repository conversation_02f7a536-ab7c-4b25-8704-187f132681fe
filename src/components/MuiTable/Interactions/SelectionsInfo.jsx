import React from 'react';
import { bool, instanceOf } from 'prop-types';

const SelectionsInfo = ({ checkboxSelection, rowSelectionModel, rows }) => {
  if (!checkboxSelection) {
    return null;
  }

  return (
    <div className="interactions__actions-selections">
      Selected
      {' '}
      <b>
        {rowSelectionModel?.length}
      </b>
      {' '}
      of
      {' '}
      {rows?.length}
    </div>
  );
};

SelectionsInfo.propTypes = {
  checkboxSelection: bool.isRequired,
  rowSelectionModel: instanceOf(Array).isRequired,
  rows: instanceOf(Array).isRequired,
};

export default SelectionsInfo;
