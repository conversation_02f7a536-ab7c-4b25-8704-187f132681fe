import React, {
  createContext, useCallback, useContext, useMemo, useState,
} from 'react';
import PropTypes, { func, instanceOf, string } from 'prop-types';
import { defaultPageSize, firstPage } from './constants';

export const MuiTableContext = createContext();

export const MuiTableProvider = ({
  children, defaultPagination, onChange,
  onChangePagination, onChangeSearch, initialSearchValue, onChangeSort,
  initialState, defaultSort,
}) => {
  const [searchValue, setSearchValue] = useState(initialSearchValue);
  const [pagination, setPagination] = useState(defaultPagination);
  const [sort, setSort] = useState(
    defaultSort?.field
    && defaultSort?.sort
      ? [defaultSort]
      : [],
  );

  const onChangeHandler = (handler, changeArguments) => {
    if (handler) {
      handler(...changeArguments);
      return;
    }

    onChange?.(...changeArguments);
  };

  const onPaginationModelChange = (paginationModel) => {
    setPagination(paginationModel);

    onChangeHandler(
      onChangePagination,
      [paginationModel, sort[0] || {}, searchValue],
    );
  };

  const onSortModelChange = (newSort) => {
    const newPagination = { page: firstPage, pageSize: pagination.pageSize };
    setPagination(newPagination);
    setSort(newSort);

    onChangeHandler(
      onChangeSort,
      [newPagination, newSort[0] || {}, searchValue],
    );
  };

  const onSearchValueChange = useCallback((newSearchValue) => {
    setPagination((prevPagination) => {
      const newPagination = { page: firstPage, pageSize: prevPagination.pageSize };

      onChangeHandler(
        onChangeSearch,
        [newPagination, sort[0] || {}, newSearchValue],
      );

      return newPagination;
    });
  }, [setPagination, onChangeSearch, sort, onChange]);

  const initialStateWithSort = useMemo(() => {
    const isDefaultSort = defaultSort && defaultSort?.field && defaultSort?.sort;

    if (isDefaultSort) {
      return {
        ...initialState,
        sorting: { sortModel: [defaultSort] },
      };
    }

    return initialState;
  }, [initialState, defaultSort]);

  const value = useMemo(() => ({
    searchValue,
    setSearchValue,
    pagination,
    setPagination,
    sort,
    setSort,
    onPaginationModelChange,
    onSortModelChange,
    onSearchValueChange,
    initialState: initialStateWithSort,
  }), [
    searchValue,
    pagination,
    sort,
    onPaginationModelChange,
    onSortModelChange,
    onSearchValueChange,
    initialStateWithSort,
  ]);

  return (
    <MuiTableContext.Provider value={value}>
      {children}
    </MuiTableContext.Provider>
  );
};

MuiTableProvider.propTypes = {
  children: PropTypes.node.isRequired,
  defaultPagination: instanceOf(Object),
  defaultSort: instanceOf(Object),
  onChangePagination: func,
  onChangeSort: func,
  onChange: func,
  onChangeSearch: func,
  initialSearchValue: string,
  initialState: instanceOf(Object),
};

MuiTableProvider.defaultProps = {
  defaultPagination: { page: firstPage, pageSize: defaultPageSize },
  defaultSort: { field: null, sort: null },
  onChangePagination: undefined,
  onChangeSort: undefined,
  onChange: undefined,
  onChangeSearch: undefined,
  initialSearchValue: '',
  initialState: {},
};

export const useMuiTableContext = () => useContext(MuiTableContext);

export default {
  MuiTableContext,
  MuiTableProvider,
  useMuiTableContext,
};
