.MuiDataGrid {
  &-row {
    &-bold {
      font-weight: 700;
    }
  }

  &-root {
    margin-top: 20px;

    .MuiDataGrid-columnHeaderTitle, .MuiDataGrid-cellContent {
      font-size: 14px;

      @media (max-width: 1600px) {
        font-size: 13px;
      }
    }

    .MuiDataGrid-columnHeaderTitle {
      font-weight: 700;
    }

    .MuiTablePagination {
      &-selectLabel, &-select, &-displayedRows {
        font-size: 14px;
        color: $dark-color-500;

        @media (max-width: 1600px) {
          font-size: 13px;
        }
      }

      &-root {
        height: 53px !important;
        width: 100%;
      }

      &-toolbar {
        display: flex;
        justify-content: space-between;
        padding-left: 16px !important;

        /* select */
        .MuiInputBase-root {
          margin-right: 10px;
          border: 1px solid $border-color;
          border-radius: 4px;
          height: 36px;
        }
      }

      &-spacer {
        display: none;
      }

      &-displayedRows {
        flex: 1;
        display: flex;
        justify-content: center;
      }
    }
  }
}

.interactions {
  display: flex;
  padding-top: 20px;

  &__actions {
    display: flex;
    justify-content: space-between;
    flex: 1;

    &-selections {
      display: flex;
      align-items: center;
      color: $dark-color-500;
      margin-left: 20px;

      b {
        margin: 0 4px;
      }
    }
  }
}

.table-paging {
  &__page-number-button, &__page-icon-button {
    min-width: 40px !important;
    max-width: 40px !important;
    min-height: 40px !important;
    max-height: 40px !important;
    font-weight: 400 !important;
    font-size: 14px !important;

    @media (max-width: 1600px) {
      font-size: 13px !important;
    }

    span {
      min-width: 40px !important;
      max-width: 40px !important;
      min-height: 40px !important;
      max-height: 40px !important;
    }
  }
}

.make-me-sticky {
  position: sticky;
  left: 0;
}

// ↓ Fixes the bag with the last column separator ↓
.MuiDataGrid-columnHeader:last-child .MuiDataGrid-columnSeparator {
  display: none;
}

// ↑ Fixes the bag with the last column separator ↑

// ↓ Do not allow to select cells ↓
.MuiDataGrid-cell:focus, .MuiDataGrid-cell:focus-within,
.MuiDataGrid-columnHeader:focus, .MuiDataGrid-columnHeader:focus-within {
  outline: none !important;
  border-color: transparent !important;
}

// ↑ Do not allow to select cells ↑

.MuiDataGrid-columnHeaderTitle {
  color: $dark-color-500;
}

.MuiDataGrid-cellContent {
  color: $dark-color-500;
}
