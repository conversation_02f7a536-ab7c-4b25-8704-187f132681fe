import React from 'react';
import {
  BsChevronDoubleLeft, BsChevronDoubleRight, BsChevronLeft, BsChevronRight,
} from 'react-icons/bs';
import {
  bool, func, number, string,
} from 'prop-types';
import { firstPage, rowCountDisabledByServer } from '../constants';
import PaginationPageSwitcherNumericButtons from './PaginationPageSwitcherNumericButtons';
import PaginationIconButton from './PaginationIconButton';

const PaginationPageSwitcher = (props) => {
  const {
    rowCount,
    page,
    pageSize,
    isFirstLastPageButtonsHidden,
    isNextPrevPageButtonsHidden,
    onPageChange,
    primaryColor,
    getCurrentThemeColors,
  } = props;

  const lastPage = rowCount === -1 ? page + 1 : Math.ceil(rowCount / pageSize);

  const isPrevPageDisabled = page === firstPage;
  const isNextPageDisabled = page >= lastPage;
  const isLastPageDisabled = page >= lastPage || rowCount === rowCountDisabledByServer;

  const handleFirstPageButtonClick = () => {
    onPageChange({}, firstPage);
  };

  const handleBackButtonClick = () => {
    const newPage = page - 1;

    if (newPage < firstPage) return;

    onPageChange({}, page - 1);
  };

  const handleNextButtonClick = () => {
    const newPage = page + 1;

    if (newPage > lastPage) return;

    onPageChange({}, newPage);
  };

  const handleNumberButtonClick = (event, newPage) => {
    if (newPage < firstPage) return;
    if (newPage > lastPage) return;

    onPageChange(event, newPage);
  };

  const handleLastPageButtonClick = () => {
    onPageChange({}, lastPage);
  };

  return (
    <div>
      <PaginationIconButton
        tooltipTitle="First Page"
        onClick={handleFirstPageButtonClick}
        isHidden={isFirstLastPageButtonsHidden}
        isDisabled={isPrevPageDisabled}
        icon={<BsChevronDoubleLeft size={19} />}
      />
      <PaginationIconButton
        tooltipTitle="Previous Page"
        onClick={handleBackButtonClick}
        isHidden={isNextPrevPageButtonsHidden}
        isDisabled={isPrevPageDisabled}
        icon={<BsChevronLeft size={19} />}
      />
      <PaginationPageSwitcherNumericButtons
        lastPage={lastPage}
        page={page}
        handleNumberButtonClick={handleNumberButtonClick}
        primaryColor={primaryColor}
        getCurrentThemeColors={getCurrentThemeColors}
      />
      <PaginationIconButton
        tooltipTitle="Next Page"
        onClick={handleNextButtonClick}
        isHidden={isNextPrevPageButtonsHidden}
        isDisabled={isNextPageDisabled}
        icon={<BsChevronRight size={19} />}
      />
      <PaginationIconButton
        tooltipTitle="Last Page"
        onClick={handleLastPageButtonClick}
        isHidden={isFirstLastPageButtonsHidden}
        isDisabled={isLastPageDisabled}
        icon={<BsChevronDoubleRight size={19} />}
      />
    </div>
  );
};

PaginationPageSwitcher.propTypes = {
  rowCount: number,
  page: number,
  pageSize: number,
  isFirstLastPageButtonsHidden: bool,
  isNextPrevPageButtonsHidden: bool,
  onPageChange: func,
  primaryColor: string,
  getCurrentThemeColors: func,
};

PaginationPageSwitcher.defaultProps = {
  rowCount: 1,
  page: 1,
  pageSize: 10,
  isFirstLastPageButtonsHidden: false,
  isNextPrevPageButtonsHidden: false,
  onPageChange: () => {},
  primaryColor: '#ffffff',
  getCurrentThemeColors: () => {},
};

export default PaginationPageSwitcher;
