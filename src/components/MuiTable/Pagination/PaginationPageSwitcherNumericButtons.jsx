import React from 'react';
import Button from '@mui/material/Button';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import { func, number, string } from 'prop-types';
import { firstPage } from '../constants';

const getButtonSelectedSx = (colors) => {
  const selectedBackgroundColor = colors[100];
  const selectedColor = colors[500];

  return {
    color: selectedColor,
    backgroundColor: selectedBackgroundColor,
    fontWeight: '700 !important',
    '&:hover': {
      color: selectedColor,
      backgroundColor: selectedBackgroundColor,
    },
  };
};

const getRegularButtonSx = (colors) => {
  const hoverBackgroundColor = colors[50];

  return {
    color: styles.darkColor500,
    '&:hover': {
      backgroundColor: hoverBackgroundColor,
    },
  };
};

const getButtonSx = (colors, isSelected) => {
  if (isSelected) return getButtonSelectedSx(colors);

  return getRegularButtonSx(colors);
};

const PaginationPageSwitcherNumericButtons = ({
  lastPage, page, handleNumberButtonClick, primaryColor, getCurrentThemeColors,
}) => {
  const colors = getCurrentThemeColors(primaryColor);

  const buttons = [];

  let pageStart = Math.max(page - 1, firstPage);
  let pageEnd = Math.min(lastPage, page + 1);

  if (page === firstPage) {
    pageEnd = Math.min(lastPage, page + 2);
  }

  if (page === lastPage) {
    pageStart = Math.max(firstPage, page - 2);
  }

  for (let p = pageStart; p <= pageEnd; p += 1) {
    const buttonVariant = p === page ? 'contained' : 'text';

    const isSelected = p === page;

    const sx = getButtonSx(colors, isSelected);

    const onClick = (e) => {
      if (isSelected) return;

      handleNumberButtonClick(e, p);
    };

    buttons.push(
      <Button
        size="small"
        className="table-paging__page-number-button"
        sx={sx}
        variant={buttonVariant}
        onClick={onClick}
        key={p}
      >
        {p}
      </Button>,
    );
  }

  if (!buttons.length) {
    return null;
  }

  return <span>{buttons}</span>;
};

PaginationPageSwitcherNumericButtons.propTypes = {
  lastPage: number,
  page: number,
  handleNumberButtonClick: func,
  primaryColor: string,
  getCurrentThemeColors: func,
};

PaginationPageSwitcherNumericButtons.defaultProps = {
  lastPage: 1,
  page: 1,
  handleNumberButtonClick: () => {},
  primaryColor: '#ffffff',
  getCurrentThemeColors: () => {},
};

export default PaginationPageSwitcherNumericButtons;
