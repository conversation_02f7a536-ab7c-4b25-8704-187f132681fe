import React from 'react';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';

const { darkColor200 } = styles;

const disableIconSx = {
  ':disabled svg path': {
    stroke: `${darkColor200} !important`,
    color: `${darkColor200} !important`,
  },
};

interface IPaginationIconButtonProps {
  icon: React.ReactNode;
  onClick: () => void;
  isDisabled: boolean;
  tooltipTitle: string;
  isHidden: boolean | undefined;
}

const PaginationIconButton = ({
  icon, onClick, isDisabled, tooltipTitle, isHidden,
}: IPaginationIconButtonProps) => {
  if (isHidden) return null;

  return (
    <Tooltip title={tooltipTitle}>
      <span>
        <IconButton
          className="table-paging__page-icon-button"
          onClick={onClick}
          sx={isDisabled ? disableIconSx : {}}
          disabled={isDisabled}
          size="large"
          data-testid={tooltipTitle.toLowerCase().replace(' ', '-')}
        >
          {icon}
        </IconButton>
      </span>
    </Tooltip>
  );
};

export default PaginationIconButton;
