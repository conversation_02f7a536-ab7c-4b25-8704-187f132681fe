import React from 'react';
import { TablePagination } from '@mui/material';
import {
  bool, func, instanceOf, number, string,
} from 'prop-types';
import PaginationPageSwitcher from './PaginationPageSwitcher';
import { firstPage } from '../constants';

const Pagination = (props) => {
  const {
    primaryColor,
    getCurrentThemeColors,
    onPaginationModelChange,
    pageSizeOptions,
    page,
    pageSize,
    rowCount,
    isPaginationDisabled,
    isFirstLastPageButtonsHidden,
    isNextPrevPageButtonsHidden,
  } = props;

  const onPageChange = (event, newPage) => {
    onPaginationModelChange({ page: newPage, pageSize });
  };

  const onRowsPerPageChange = (event) => {
    const { target: { value } } = event;
    onPaginationModelChange({ page: firstPage, pageSize: value });
  };

  const maxTo = page * pageSize;
  const from = maxTo - pageSize;

  const
    to = maxTo > rowCount ? rowCount : maxTo;

  if (isPaginationDisabled) {
    return null;
  }

  const maxPageNumber = Math.max(0, Math.ceil(rowCount / pageSize) - 1);
  const validPageNumber = Math.max(0, page > maxPageNumber ? maxPageNumber : page);

  return (
    <TablePagination
      onPageChange={onPageChange}
      onRowsPerPageChange={onRowsPerPageChange}
      page={validPageNumber}
      rowsPerPage={pageSize}
      rowsPerPageOptions={pageSizeOptions}
      count={rowCount}
      component="div"
      data-testid="pagination"
      ActionsComponent={() => (
        <PaginationPageSwitcher
          primaryColor={primaryColor}
          getCurrentThemeColors={getCurrentThemeColors}
          page={page}
          pageSize={pageSize}
          rowCount={rowCount}
          isFirstLastPageButtonsHidden={isFirstLastPageButtonsHidden}
          isNextPrevPageButtonsHidden={isNextPrevPageButtonsHidden}
          onPageChange={onPageChange}
        />
      )}
      labelDisplayedRows={() => `${from} - ${to} of ${rowCount !== -1 ? rowCount : `more than ${to}`}`}
    />
  );
};

Pagination.propTypes = {
  primaryColor: string,
  getCurrentThemeColors: func,
  onPaginationModelChange: func,
  pageSizeOptions: instanceOf(Array),
  page: number,
  pageSize: number,
  rowCount: number,
  isPaginationDisabled: bool,
  isNextPrevPageButtonsHidden: bool.isRequired,
  isFirstLastPageButtonsHidden: bool.isRequired,
};

Pagination.defaultProps = {
  primaryColor: '#ffffff',
  getCurrentThemeColors: () => {
  },
  onPaginationModelChange: () => {
  },
  pageSizeOptions: [1, 2, 3],
  page: 1,
  pageSize: 10,
  rowCount: 1,
  isPaginationDisabled: false,
};

export default Pagination;
