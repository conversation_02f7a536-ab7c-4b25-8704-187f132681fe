export const firstPage = 1;
export const rowCountDisabledByServer = -1;
export const defaultPageSize = 10;
export const pageFieldName = 'page';
export const pageSizeFieldName = 'pageSize';
export const fieldFieldName = 'field';
export const sortFieldName = 'sort';
export const searchFieldName = 'search';
export const abortErrorName = 'CanceledError';

export const rowModes = {
  edit: 'edit',
  view: 'view',
};

export default {
  firstPage,
  rowCountDisabledByServer,
  defaultPageSize,
  pageFieldName,
  pageSizeFieldName,
  fieldFieldName,
  sortFieldName,
  searchFieldName,
  abortErrorName,
  rowModes,
};
