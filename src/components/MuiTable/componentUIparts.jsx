/* eslint-disable import/prefer-default-export */
import { styled } from '@mui/material';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import { DataGrid } from '@mui/x-data-grid';
import { forIn } from 'lodash';

export const StyledDataGrid = styled(DataGrid)(({
  theme,
  fixed,
  width,
  fixedColumns,
  fixedColumnsRight,
  getCurrentThemeColors,
  primaryColor,
  rows,
  columns,
  maxTableHeight,
  checkboxSelection,
}) => {
  const queries = {};

  let leftOffset = 0;
  let rightOffset = 0;

  forIn(fixedColumns, (value, key) => {
    queries[`
      .MuiDataGrid-cell:nth-of-type(${Number(key) + 1}),
      .MuiDataGrid-columnHeader:nth-of-type(${Number(key) + 1})
      `] = {
      position: 'sticky',
      filter: 'drop-shadow(rgba(140, 140, 140, 0.1) 8px 0px 8px)',
      zIndex: 9,
      left: leftOffset,
    };

    leftOffset += value;
  });

  forIn(fixedColumnsRight, (value, key) => {
    const columnsLength = checkboxSelection ? columns.length + 1 : columns.length;
    const columnIndex = Number(columnsLength) - Number(key);
    queries[`
        .MuiDataGrid-cell:nth-of-type(${columnIndex}),
        .MuiDataGrid-columnHeader:nth-of-type(${columnIndex})
      `] = {
      position: 'sticky',
      zIndex: 9,
      right: rightOffset,
      ...(Math.max(...Object.keys(fixedColumnsRight)) === Number(key) && {
        filter: 'drop-shadow(rgba(140, 140, 140, 0.1) -8px 0px 8px)',
      }),
    };

    rightOffset += value;
  });

  const fixedColumnsStyles = {
    '.MuiDataGrid-columnHeaders, .MuiDataGrid-virtualScroller': {
      width: fixed ? width : 'auto',
      overflow: 'hidden!important',
      position: 'static',

      '.MuiDataGrid-columnHeadersInner': {
        height: 56,
        '&, & > div': {
          position: 'absolute',
          zIndex: 9,
        },
      },
    },
    '.MuiDataGrid-columnHeadersInner': {
      transform: 'none!important',
    },
  };

  const scrollStyles = {
    '&::-webkit-scrollbar': {
      width: '6px!important',
      backgroundColor: 'transparent !important',
      appearance: 'none',
      height: '6px',
    },
    '& ::-webkit-scrollbar-track': {
      position: 'absolute !important',
      left: '0 !important',
      backgroundColor: 'transparent !important',
    },
    '&::-webkit-scrollbar-thumb': {
      right: '4px!important',
      position: ' absolute!important',
      borderWidth: '0!important',
      padding: ' 0!important',
      backgroundColor: 'rgba(0, 0, 0, 0.2)!important',
      borderRadius: '3px!important',
    },
  };

  const tableStyles = {
    '.MuiDataGrid-main': {
      overflow: 'auto',
      maxHeight: maxTableHeight || 'auto',
      ...queries,
      ...scrollStyles,
      ...(fixed && fixedColumnsStyles),
      '.MuiDataGrid-columnHeaders': {
        backgroundColor: `${styles.lightColor100}!important`,
      },
      '.MuiDataGrid-columnHeader': {
        backgroundColor: `${styles.lightColor100}!important`,
        borderColor: styles.lightColor200,
      },
      '.MuiDataGrid-virtualScroller': {
        ...scrollStyles,
      },

      '.MuiDataGrid-cell': {
        borderColor: 'transparent',
        backgroundColor: theme.palette.background.default,
      },

      '.MuiDataGrid-row': {
        '&:nth-child(even) .MuiDataGrid-cell': {
          background: `${styles.lightColor100}`,
        },

        '&:hover .MuiDataGrid-cell': {
          backgroundColor: `${getCurrentThemeColors(primaryColor)[50]}!important`,
        },
      },
    },
  };

  if (rows?.length === 0) {
    tableStyles['.MuiDataGrid-main']['.MuiDataGrid-overlayWrapper'] = {
      height: 335,
    };
  }

  return tableStyles;
});
