import { renderHook } from '@testing-library/react';
import useMuiTableAbortController from './useMuiTableAbortController';

describe('useMuiTableAbortController', () => {
  beforeEach(() => {
    // @ts-ignore
    jest.spyOn(window, 'AbortController').mockImplementation(() => ({
      abort: jest.fn(),
      signal: 'signal',
    }));
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('should initialize without abort controller', () => {
    const { result } = renderHook(() => useMuiTableAbortController());

    expect(result.current.getController()).toBeNull();
  });

  test('should create a new abort controller', () => {
    const { result } = renderHook(() => useMuiTableAbortController());
    const { controller, signal } = result.current.setNewController();

    expect(result.current.getController()).toEqual(controller);
    expect(result.current.getSignal()).toEqual(signal);
  });

  test('should cancel the current request', () => {
    const { result } = renderHook(() => useMuiTableAbortController());
    const { controller } = result.current.setNewController();
    result.current.cancelPreviousRequest();

    expect(controller.abort).toHaveBeenCalled();
  });

  test('should not cancel the request if there is no active abort controller', () => {
    const { result } = renderHook(() => useMuiTableAbortController());
    result.current.cancelPreviousRequest();

    expect(jest.fn()).not.toHaveBeenCalled();
  });

  test('should create a new abort controller', () => {
    const { result } = renderHook(() => useMuiTableAbortController());
    const initialController = result.current.getController();
    const initialSignal = result.current.getSignal();

    const { controller: newController, signal: newSignal } = result.current.setNewController();

    expect(initialController).toBeNull();
    expect(initialSignal).toBeUndefined();
    expect(initialController).not.toEqual(newController);
    expect(initialSignal).not.toEqual(newSignal);
  });

  test('should not create a new abort controller when the component is updated', () => {
    const { result, rerender } = renderHook(() => useMuiTableAbortController());
    result.current.setNewController();

    const initialController = result.current.getController();
    const initialSignal = result.current.getSignal();

    rerender();

    expect(result.current.getController()).toEqual(initialController);
    expect(result.current.getSignal()).toEqual(initialSignal);
  });
});
