import React from 'react';
import * as ReactRouterDom from 'react-router-dom';
import { renderHook } from '@testing-library/react';
import useSimManagementClientSearchParams from './useMuiTableSearchParams';

const setupSearchParamsMock = () => {
  const setSearchParams = jest.fn();

  const useSearchParamsSpy = jest.spyOn(ReactRouterDom, 'useSearchParams');

  useSearchParamsSpy.mockReturnValue([{ get: jest.fn(), set: jest.fn() }, setSearchParams]);

  return { setSearchParams };
};

const render = () => {
  const { result } = renderHook(() => useSimManagementClientSearchParams());

  return result.current;
};

describe('useMuiTableSearchParams', () => {
  test('should update the search params when setParamsToUrl is called', () => {
    const { setSearchParams } = setupSearchParamsMock();

    const { setParamsToUrl } = render();

    setParamsToUrl(2, 25, 'name', 'asc', 'searchtext');

    expect(setSearchParams).toHaveBeenCalledWith(expect.any(URLSearchParams));
    expect(setSearchParams.mock.calls[0][0].toString()).toEqual('page=2&pageSize=25&field=name&sort=asc&search=searchtext');
  });

  test('should not update the search params when setParamsToUrl is called with no params', () => {
    const { setSearchParams } = setupSearchParamsMock();

    const { setParamsToUrl } = render();

    setParamsToUrl(undefined, undefined, undefined, undefined, undefined);

    expect(setSearchParams).not.toHaveBeenCalled();
  });

  test('should get default params from the URL when getParamsFromUrl is called', () => {
    const getSearchParams = jest.fn((field) => (
      new URLSearchParams('page=2&pageSize=25&field=name&sort=asc&search=searchtext').get(field)
    ));

    const useSearchParamsSpy = jest.spyOn(ReactRouterDom, 'useSearchParams');

    useSearchParamsSpy.mockReturnValue([{ get: getSearchParams }, jest.fn()]);

    const { getParamsFromUrl } = render();

    const params = getParamsFromUrl();

    expect(params).toEqual({
      page: '2',
      pageSize: '25',
      field: 'name',
      sort: 'asc',
      search: 'searchtext',
    });
  });

  test('should get default pagination from url when defaultPagination is called', () => {
    const getSearchParams = jest.fn((field) => new URLSearchParams('page=2&pageSize=25').get(field));

    const useSearchParamsSpy = jest.spyOn(ReactRouterDom, 'useSearchParams');

    useSearchParamsSpy.mockReturnValue([{ get: getSearchParams }, null]);

    const { defaultPagination } = render();

    expect(defaultPagination).toEqual({
      page: 2,
      pageSize: 25,
    });
  });

  test('should get default values for default pagination when url is empty', () => {
    const getSearchParams = jest.fn((field) => new URLSearchParams('').get(field));

    const useSearchParamsSpy = jest.spyOn(ReactRouterDom, 'useSearchParams');

    useSearchParamsSpy.mockReturnValue([{ get: getSearchParams }, null]);

    const { defaultPagination } = render();

    expect(defaultPagination).toEqual({
      page: 1,
      pageSize: 10,
    });
  });

  test('should get default sort when defaultSort is called', () => {
    const getSearchParams = jest.fn((field) => new URLSearchParams('field=name&sort=asc').get(field));

    const useSearchParamsSpy = jest.spyOn(ReactRouterDom, 'useSearchParams');

    useSearchParamsSpy.mockReturnValue([{ get: getSearchParams }, null]);

    const { defaultSort } = render();

    expect(defaultSort).toEqual({
      field: 'name',
      sort: 'asc',
    });
  });

  test('should get empty default sort when url is empty', () => {
    const getSearchParams = jest.fn((field) => new URLSearchParams('').get(field));

    const useSearchParamsSpy = jest.spyOn(ReactRouterDom, 'useSearchParams');

    useSearchParamsSpy.mockReturnValue([{ get: getSearchParams }, null]);

    const { defaultSort } = render();

    expect(defaultSort).toEqual({
      field: null,
      sort: null,
    });
  });

  test('should get initial search value when initialSearchValue is called', () => {
    const getSearchParams = jest.fn((field) => new URLSearchParams('search=searchtext').get(field));

    const useSearchParamsSpy = jest.spyOn(ReactRouterDom, 'useSearchParams');

    useSearchParamsSpy.mockReturnValue([{ get: getSearchParams }, null]);

    const { initialSearchValue } = render();

    expect(initialSearchValue).toEqual('searchtext');
  });

  test('should get null as initial search value when url is empty', () => {
    const getSearchParams = jest.fn((field) => new URLSearchParams('').get(field));

    const useSearchParamsSpy = jest.spyOn(ReactRouterDom, 'useSearchParams');

    useSearchParamsSpy.mockReturnValue([{ get: getSearchParams }, jest.fn()]);

    const { initialSearchValue } = render();

    expect(initialSearchValue).toEqual(null);
  });
});
