import { useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  firstPage, defaultPageSize as defaultPageSizeConstant, pageSizeFieldName, sortFieldName,
  searchFieldName, pageFieldName, fieldFieldName,
} from '../constants';

interface IUseMuiTableSearchParamsReturn {
  setParamsToUrl: (
    page: string | number, pageSize: string | number, field?: string, sort?: string,
    search?: string,
  ) => void
  getParamsFromUrl: () => ({
    page: string | number, pageSize: string | number, field: string | null, sort: string | null,
    search: string | null
  })
  defaultPagination: { page: number, pageSize: number }
  defaultSort: { field: string | null, sort: string | null }
  initialSearchValue: string | null
}

const useMuiTableSearchParams = (
  defaultPage = firstPage,
  defaultPageSize = defaultPageSizeConstant,
) => {
  const [searchParams, setSearchParams] = useSearchParams();

  const setParamsToUrl = (page, pageSize, field, sort, search) => {
    if (!page && !pageSize && !field && !sort && !search) {
      return;
    }

    const newSearchParams = new URLSearchParams();

    if (page) {
      newSearchParams.set(pageFieldName, page);
    }
    if (pageSize) {
      newSearchParams.set(pageSizeFieldName, pageSize);
    }
    if (field) {
      newSearchParams.set(fieldFieldName, field);
    }
    if (sort) {
      newSearchParams.set(sortFieldName, sort);
    }
    if (search) {
      newSearchParams.set(searchFieldName, search);
    }

    setSearchParams(newSearchParams);
  };

  const getParamsFromUrl = () => {
    const page = searchParams.get(pageFieldName) || defaultPage;
    const pageSize = searchParams.get(pageSizeFieldName) || defaultPageSize;
    const field = searchParams.get(fieldFieldName);
    const sort = searchParams.get(sortFieldName);
    const search = searchParams.get(searchFieldName);

    return {
      page, pageSize, field, sort, search,
    };
  };

  const defaultPagination = useMemo(() => ({
    page: Number(searchParams.get(pageFieldName)) || defaultPage,
    pageSize: Number(searchParams.get(pageSizeFieldName)) || defaultPageSize,
  }), []);

  const defaultSort = useMemo(() => ({
    field: searchParams.get(fieldFieldName),
    sort: searchParams.get(sortFieldName),
  }), []);

  const initialSearchValue = useMemo(() => searchParams.get(searchFieldName), []);

  return <IUseMuiTableSearchParamsReturn>{
    setParamsToUrl,
    getParamsFromUrl,
    defaultPagination,
    defaultSort,
    initialSearchValue,
  };
};

export default useMuiTableSearchParams;
