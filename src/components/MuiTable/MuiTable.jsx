import React, {
  useEffect, useMemo, useState,
} from 'react';
import Box from '@mui/material/Box';
import { useGridApiRef } from '@mui/x-data-grid';
import {
  bool, element, func, instanceOf, number, oneOf, oneOfType, string,
} from 'prop-types';
import { keys } from 'lodash';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import Pagination from './Pagination';
import { rowCountDisabledByServer } from './constants';
import Interactions from './Interactions';
import { StyledDataGrid } from './componentUIparts';
import { handleRowEditStartOrStop } from './utils';
import NoData from './NoData';
import './MuiTable.scss';
import { useMuiTableContext } from './MuiTableContext';

const processColumn = (item) => ({
  disableColumnMenu: true,
  sortable: false,
  hideable: false,
  filterable: false,
  ...item,
});

const getEmptySx = (colors) => ({
  '.MuiDataGrid-row:hover': { backgroundColor: colors[50] },
  '.MuiDataGrid-columnHeaderTitle': {
    // ↓ make it possible to do two line headers ↓
    overflow: 'hidden',
    lineHeight: '20px',
    whiteSpace: 'normal',
    // ↑ make it possible to do two line headers ↑
  },
});
const getDisabledPaginationSx = (colors) => (
  { '.MuiDataGrid-footerContainer': { display: 'none' }, ...getEmptySx(colors) }
);

const getSx = (colors, isPaginationDisabled) => {
  if (isPaginationDisabled) return getDisabledPaginationSx(colors);

  return getEmptySx(colors);
};

const MuiTable = (props) => {
  const {
    columns, rows, rowCount, loading, rowHeight,
    pageSizeOptions, maxTableHeight,
    isPaginationDisabled, isFirstLastPageButtonsHidden, isNextPrevPageButtonsHidden,
    getCurrentThemeColors, primaryColor, isVisibleSearchInput,
    paginationMode, sortingMode, Actions, ActionsLeft,
    fixedColumns, fixedColumnsRight, onRowEditStart, onRowEditStop, slots,
    slotProps, sx, checkboxSelection, rowSelectionModel, noDataConfig, ...otherProps
  } = props;

  const muiTableContextValue = useMuiTableContext();

  const {
    searchValue,
    setSearchValue,
    pagination,
    sort,
    onPaginationModelChange,
    onSortModelChange,
    onSearchValueChange,
    initialState,
  } = muiTableContextValue;

  const [totalWidth, setTotalWidth] = useState(0);

  const noDataComponent = () => (
    <NoData
      icon={noDataConfig?.icon}
      title={noDataConfig?.title}
      additionalItem={noDataConfig?.additionalItem}
      description={noDataConfig?.description}
      primaryColor={primaryColor}
      getCurrentThemeColors={getCurrentThemeColors}
    />
  );

  const formattedColumns = useMemo(() => (
    columns
      .filter((col) => !col.hide)
      .map(processColumn)
  ), [columns]);

  const combinedSx = useMemo(() => {
    const colors = getCurrentThemeColors(primaryColor);

    return {
      ...getSx(colors, isPaginationDisabled),
      ...sx,
    };
  }, [sx, isPaginationDisabled]);

  const isFixed = !!keys({ ...fixedColumns, ...fixedColumnsRight }).length;

  const tableRef = useGridApiRef();

  useEffect(() => {
    if (tableRef.current) {
      const width = tableRef.current.getAllColumns()
        .reduce((total, next) => total + next.computedWidth, 0);
      setTotalWidth(width);
    }
  }, []);

  return (
    <div data-testid="mui-table">
      <Box>
        <Interactions
          pagination={pagination}
          sort={sort}
          searchValue={searchValue}
          onSearchValueChange={onSearchValueChange}
          setSearchValue={setSearchValue}
          isVisibleSearchInput={isVisibleSearchInput}
          Actions={Actions}
          ActionsLeft={ActionsLeft}
          rows={rows}
          checkboxSelection={checkboxSelection}
          rowSelectionModel={rowSelectionModel}
          {...muiTableContextValue}
          {...props}
        />
        <StyledDataGrid
          apiRef={tableRef}
          fixed={isFixed}
          width={totalWidth}
          maxTableHeight={maxTableHeight}
          getCurrentThemeColors={getCurrentThemeColors}
          primaryColor={primaryColor}
          fixedColumns={fixedColumns}
          fixedColumnsRight={fixedColumnsRight}
          sx={combinedSx}
          rows={rows}
          columns={formattedColumns}
          rowCount={rowCount}
          loading={loading}
          rowHeight={rowHeight}
          paginationModel={{ pageSize: pagination.pageSize, page: pagination.page - 1 }}
          onPaginationModelChange={onPaginationModelChange}
          sortModel={sort}
          sortingOrder={['asc', 'desc', null]}
          onSortModelChange={onSortModelChange}
          paginationMode={paginationMode}
          sortingMode={sortingMode}
          onRowEditStart={onRowEditStart}
          onRowEditStop={onRowEditStop}
          autoHeight={!maxTableHeight}
          slots={{
            pagination: Pagination,
            noRowsOverlay: noDataComponent,
            ...slots,
          }}
          checkboxSelection={checkboxSelection}
          rowSelectionModel={rowSelectionModel}
          slotProps={{
            pagination: {
              page: pagination.page,
              pageSize: pagination.pageSize,
              isPaginationDisabled,
              rowCount,
              pageSizeOptions,
              primaryColor,
              getCurrentThemeColors,
              onPaginationModelChange,
              isFirstLastPageButtonsHidden,
              isNextPrevPageButtonsHidden,
            },
            ...slotProps,
          }}
          initialState={initialState}
          {...otherProps}
        />
      </Box>
    </div>
  );
};

MuiTable.propTypes = {
  columns: instanceOf(Array),
  rows: instanceOf(Array),
  rowCount: number,
  loading: bool,
  rowHeight: number,
  pageSizeOptions: instanceOf(Array),
  isPaginationDisabled: bool,
  isFirstLastPageButtonsHidden: bool,
  isNextPrevPageButtonsHidden: bool,
  getCurrentThemeColors: func,
  primaryColor: string,
  isVisibleSearchInput: bool,
  checkboxSelection: bool,
  maxTableHeight: string,
  paginationMode: oneOf(['client', 'server']),
  sortingMode: oneOf(['client', 'server']),
  Actions: oneOfType([element, func]),
  ActionsLeft: oneOfType([element, func]),
  fixedColumns: instanceOf(Object),
  fixedColumnsRight: instanceOf(Object),
  onRowEditStop: func,
  onRowEditStart: func,
  slots: instanceOf(Object),
  slotProps: instanceOf(Object),
  sx: instanceOf(Object),
  rowSelectionModel: instanceOf(Array),
  noDataConfig: instanceOf(Object),
};

MuiTable.defaultProps = {
  columns: [],
  rows: [],
  rowCount: rowCountDisabledByServer,
  loading: false,
  rowHeight: 54,
  pageSizeOptions: [10, 15, 20, 30, 40, 50],
  isPaginationDisabled: false,
  isFirstLastPageButtonsHidden: false,
  isNextPrevPageButtonsHidden: false,
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
  primaryColor: '#ffffff',
  isVisibleSearchInput: false,
  checkboxSelection: false,
  paginationMode: 'server',
  sortingMode: 'server',
  Actions: null,
  ActionsLeft: null,
  fixedColumns: {},
  fixedColumnsRight: {},
  onRowEditStop: handleRowEditStartOrStop,
  onRowEditStart: handleRowEditStartOrStop,
  slots: undefined,
  slotProps: undefined,
  sx: {},
  rowSelectionModel: [],
  noDataConfig: undefined,
  maxTableHeight: null,
};

export default MuiTable;
