import React from 'react';
import { render } from '@testing-library/react';
import MuiTable from './MuiTable';
import { MuiTableProvider } from './MuiTableContext';

jest.mock('@mui/x-data-grid', () => {
  const { DataGrid } = jest.requireActual('@mui/x-data-grid');
  return {
    ...jest.requireActual('@mui/x-data-grid'),
    DataGrid: (props) => <DataGrid {...props} disableVirtualization />,
  };
});

const mockOnChange = jest.fn();
const mockOnChangeSort = jest.fn();

const columns = [
  {
    field: 'id', headerName: 'ID Header Name', width: 180, sortable: true,
  },
  { field: 'name', headerName: 'Name', width: 180 },
];

const rows = [
  { id: 1, name: 'Tesla' },
  { id: 2, name: 'Kyivstar' },
];

const defaultProps = {
  columns,
  rows,
  showFirstLastPageButtons: true,
};

const defaultContextValue = {
  onChange: mockOnChange,
  onChangeSort: mockOnChangeSort,
};

describe('MuiTable sorting', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should pass default sorting value but not call onSortChange', () => {
    render(
      <MuiTableProvider
        defaultSort={{ field: 'id', sort: 'asc' }}
        {...defaultContextValue}
      >
        <MuiTable {...defaultProps} />
      </MuiTableProvider>,
    );

    expect(mockOnChangeSort).not.toHaveBeenCalled();
  });
});
