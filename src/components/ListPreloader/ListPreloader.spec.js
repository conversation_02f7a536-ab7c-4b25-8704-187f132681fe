import React from 'react';
import { render } from '@testing-library/react';

import ListPreloader from './ListPreloader';
import preloaderIModifier from './constants';

describe('ListPreloader:', () => {
  test('should be list preloader item in the DOM', () => {
    const { getByTestId } = render(<ListPreloader />);

    const preloaderItem = getByTestId(preloaderIModifier);

    expect(preloaderItem).toBeInTheDocument();
  });
});
