import React from 'react';
import PropTypes from 'prop-types';
import { Skeleton } from '@mui/material';

import preloaderIModifier from './constants';

const ListPreloader = ({ rowAmount }) => [...Array(rowAmount)].map((el, elNumber) => {
  const isFirstEl = elNumber === 1;

  return (
    <Skeleton
      /* eslint-disable-next-line react/no-array-index-key */
      key={`${preloaderIModifier}${elNumber}`}
      variant="rect"
      className="list-preloader__i"
      animation="wave"
      data-testid={isFirstEl ? preloaderIModifier : ''}
    />
  );
});

ListPreloader.propTypes = {
  rowAmount: PropTypes.number,
};

ListPreloader.defaultProps = {
  rowAmount: 3,
};

export default ListPreloader;
