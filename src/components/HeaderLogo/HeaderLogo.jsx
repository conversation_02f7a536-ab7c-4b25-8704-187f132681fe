import React from 'react';
import PropTypes from 'prop-types';
import { Link as RouterLink } from 'react-router-dom';

import './HeaderLogo.scss';

const HeaderLogo = ({ logo, isMob, coreUrl }) => {
  const logoClass = isMob ? 'header__logo-mob' : 'header__logo';
  const img = <img src={logo} className={logoClass} alt="logo" height={40} />;

  return coreUrl ? (
    <a href={coreUrl}>
      {img}
    </a>
  ) : (
    <RouterLink to="/" />
  );
};

HeaderLogo.propTypes = {
  logo: PropTypes.string.isRequired,
  isMob: PropTypes.bool,
  coreUrl: PropTypes.string,
};

HeaderLogo.defaultProps = {
  coreUrl: '',
};

HeaderLogo.defaultProps = {
  isMob: false,
};

export default HeaderLogo;
