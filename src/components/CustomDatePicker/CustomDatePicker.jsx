import React from 'react';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { styled, TextField } from '@mui/material';
import { GrCalendar } from 'react-icons/gr';
import PropTypes from 'prop-types';

const OpenPickerIcon = () => <GrCalendar />;

const CustomDatePicker = ({
  date, setDate, disabled, minDate, maxDate,
}) => {
  const StyledDatePicker = styled(DatePicker)({
    '.MuiInputBase-root': {
      width: 111,
      padding: 0,
    },

    '.MuiInputAdornment-root': {
      left: -7,
    },

    '.MuiSvgIcon-root': {
      width: 26,
      height: 26,
    },

    '.MuiInputBase-input': {
      paddingLeft: 5,
    },
  });

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <StyledDatePicker
        disabled={!!disabled}
        views={['month', 'year']}
        label="Period"
        value={date}
        onChange={(newDate) => setDate(newDate)}
        inputFormat="MMM-YY"
        openTo="month"
        renderInput={(param) => <TextField {...param} ico helperText={null} />}
        closeOnSelect={false}
        components={{ OpenPickerIcon }}
        minDate={minDate}
        maxDate={maxDate}
      />
    </LocalizationProvider>
  );
};

CustomDatePicker.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  date: PropTypes.any.isRequired,
  setDate: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  minDate: PropTypes.instanceOf(Date),
  maxDate: PropTypes.instanceOf(Date),
};

CustomDatePicker.defaultProps = {
  disabled: false,
  minDate: null,
  maxDate: null,
};

export default CustomDatePicker;
