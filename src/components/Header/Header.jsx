import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';
import { IconButton } from '@mui/material';
import isEmptyObject from 'core/utilities/isEmptyObject';
import { Gr<PERSON><PERSON> } from 'react-icons/gr';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

import SidebarToggle from '../Sidebar/SidebarToggle';
import HeaderLogo from '../HeaderLogo';
import HeaderMenu from './HeaderMenu';
import UserInfo from './UserInfo';
import ScheduleMeeting from './ScheduleMeeting';
import ActionButtonWithTooltip from '../ActionButtonWithTooltip';

import './Header.scss';
import HeaderBreadcrumbs from './HeaderBreadcrumbs';

const Header = ({
  signOut,
  userName,
  userEmail,
  primaryColor,
  logo,
  logoMob,
  getCurrentThemeColors,
  darkColor500,
  breadcrumbs,
  coreUrl,
  isSidebar,
  headerMenuData,
  applicationsData,
  administrationData,
  notificationData,
  notificationCount,
  hubspotMeetingUrl,
  isLoadingUserInfo,
  isLoadingApplicationsManagementData,
  isLoadingShortApplications,
  collapseSidebar,
  toggleSidebar,
  collapsed,
}) => {
  const [open, setModalOpen] = useState(false);
  const navigate = useNavigate();
  const notificationstyle = {
    backgroundColor: getCurrentThemeColors(primaryColor)[500],
    color: '#fff',
    fontSize: '12px',
    fontWeight: '700',
    borderRadius: '50%',
    position: 'absolute',
    top: '3px',
    right: '-2px',
    minHeight: '20px',
    paddingInline: '3px',
    minWidth: '20px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  };
  const administrationIcon = <img src={administrationData.icon} width={24} height={24} alt="adminstartion" />;
  const NotificationIcon = <img src={notificationData.icon} width={24} height={24} alt="notification" />;

  const headerMenuBtn = coreUrl ? (
    <IconButton
      className="header__menu-btn"
      href={administrationData.url}
    >
      {administrationIcon}
    </IconButton>
  ) : (
    <IconButton
      className="header__menu-btn"
      onClick={() => navigate(administrationData.url)}
    >
      {administrationIcon}
    </IconButton>
  );

  const NotificationMenuBtn = coreUrl ? (
    <IconButton
      className="header__menu-btn"
      href={notificationData?.url}
      sx={{
        '.notificationCount': notificationCount > 0 ? notificationstyle : {},
      }}
    >
      <span className="notificationCount">{notificationCount > 0 ? notificationCount : ''}</span>
      {NotificationIcon}
    </IconButton>
  ) : (
    <IconButton
      className="header__menu-btn"
      onClick={() => navigate(notificationData?.url)}
      sx={{
        '.notificationCount': notificationCount > 0 ? notificationstyle : {},
      }}
    >
      <span className="notificationCount">{notificationCount > 0 ? notificationCount : ''}</span>
      {NotificationIcon}
    </IconButton>
  );

  return (
    <header className="header">
      <div className="header__logo-wrap">
        {isSidebar
          ? (
            <SidebarToggle
              primaryColor={primaryColor}
              getCurrentThemeColors={getCurrentThemeColors}
              collapseSidebar={collapseSidebar}
              toggleSidebar={toggleSidebar}
              collapsed={collapsed}
            />
          )
          : null}
        <HeaderLogo
          logo={logo}
          coreUrl={coreUrl}
        />
        <HeaderLogo
          logo={logoMob}
          isMob
          coreUrl={coreUrl}
        />
      </div>
      <HeaderBreadcrumbs breadcrumbs={breadcrumbs} />
      <div className="header__menu-wrap">
        {!isEmptyObject(notificationData) ? NotificationMenuBtn : null}
        {!isEmptyObject(administrationData) ? headerMenuBtn : null}
        {hubspotMeetingUrl && (
          <ActionButtonWithTooltip
            title="Schedule a meeting"
            placement="bottom"
            className="header__menu-meeting-btn"
            color={primaryColor}
            action={() => setModalOpen(true)}
            icon={<GrPhone size={24} />}
            getCurrentThemeColors={getCurrentThemeColors}
          />
        )}
        <HeaderMenu
          primaryColor={primaryColor}
          logo={logo}
          getCurrentThemeColors={getCurrentThemeColors}
          darkColor500={darkColor500}
          coreUrl={coreUrl}
          headerMenuData={headerMenuData}
          applicationsData={applicationsData}
          isLoadingApplicationsManagementData={isLoadingApplicationsManagementData}
          isLoadingShortApplications={isLoadingShortApplications}
        />
        <UserInfo
          signOut={signOut}
          userName={userName}
          userEmail={userEmail}
          primaryColor={primaryColor}
          getCurrentThemeColors={getCurrentThemeColors}
          isLoadingUserInfo={isLoadingUserInfo}
        />
      </div>
      {hubspotMeetingUrl && (
        <ScheduleMeeting
          opened={open}
          handleOpen={setModalOpen}
          hubspotMeetingUrl={hubspotMeetingUrl}
          container={() => document.getElementById('header')}
        />
      )}
    </header>
  );
};

Header.propTypes = {
  signOut: PropTypes.func.isRequired,
  userName: PropTypes.string.isRequired,
  userEmail: PropTypes.string.isRequired,
  primaryColor: PropTypes.string.isRequired,
  logo: PropTypes.string.isRequired,
  logoMob: PropTypes.string.isRequired,
  getCurrentThemeColors: PropTypes.func,
  darkColor500: PropTypes.string.isRequired,
  breadcrumbs: PropTypes.instanceOf(Object),
  coreUrl: PropTypes.string,
  isSidebar: PropTypes.bool,
  headerMenuData: PropTypes.instanceOf(Array),
  applicationsData: PropTypes.instanceOf(Array),
  administrationData: PropTypes.instanceOf(Object),
  notificationData: PropTypes.instanceOf(Object),
  notificationCount: PropTypes.number,
  hubspotMeetingUrl: PropTypes.string,
  isLoadingUserInfo: PropTypes.bool,
  isLoadingApplicationsManagementData: PropTypes.bool,
  isLoadingShortApplications: PropTypes.bool,
  collapseSidebar: PropTypes.func,
  toggleSidebar: PropTypes.func,
  collapsed: PropTypes.bool,
};

Header.defaultProps = {
  breadcrumbs: null,
  coreUrl: '',
  isSidebar: true,
  headerMenuData: [],
  applicationsData: [],
  administrationData: {},
  notificationData: {},
  notificationCount: 0,
  hubspotMeetingUrl: '',
  getCurrentThemeColors: (hex) => getBrandColors(hex),
  isLoadingUserInfo: false,
  isLoadingApplicationsManagementData: false,
  isLoadingShortApplications: false,
  collapseSidebar: () => { },
  toggleSidebar: () => { },
  collapsed: true,
};

export default Header;
