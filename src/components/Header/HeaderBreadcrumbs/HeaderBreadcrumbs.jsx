import React from 'react';
import PropTypes from 'prop-types';
import { useLocation, matchPath } from 'react-router-dom';

import { Typography } from '@mui/material';
import Breadcrumbs from '@mui/material/Breadcrumbs';

import isEmptyObject from 'core/utilities/isEmptyObject';
import './HeaderBreadcrumbs.scss';

const HeaderBreadcrumbs = ({ breadcrumbs }) => {
  const { pathname } = useLocation();
  const getConfigForCurrentLocation = () => {
    let config;
    const setConfigForCurrentLocation = (el) => {
      if (matchPath({ path: el.path }, pathname)) {
        config = el;
      }
    };

    breadcrumbs.some(setConfigForCurrentLocation);

    return config;
  };

  const configForCurrentLocation = getConfigForCurrentLocation();

  return !isEmptyObject(configForCurrentLocation)
    ? (
      <Breadcrumbs aria-label="breadcrumb">
        <div className="header-breadcrumbs__wrap">
          {configForCurrentLocation.icon}
          <Typography variant="body1" className="header-breadcrumbs">
            {configForCurrentLocation.breadcrumb}
            {configForCurrentLocation.additionalBreadcrumb
             && <span className="header-breadcrumbs__additional">{configForCurrentLocation.additionalBreadcrumb}</span>}
          </Typography>
        </div>
      </Breadcrumbs>
    ) : '';
};

HeaderBreadcrumbs.propTypes = {
  breadcrumbs: PropTypes.instanceOf(Object).isRequired,
};

export default HeaderBreadcrumbs;
