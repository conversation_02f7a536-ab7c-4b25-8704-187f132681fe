import React from 'react';
import PropTypes from 'prop-types';
import { Link as RouterLink } from 'react-router-dom';
import { AiOutlineHome } from 'react-icons/ai';
import { GrApps } from 'react-icons/gr';
import { withStyles } from '@mui/styles';
import {
  Popover,
  IconButton,
  Typography,
  Box,
} from '@mui/material';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import CustomCircularProgress from '../../CustomCircularProgress';

import './HeaderMenu.scss';

const HeaderMenu = ({
  primaryColor,
  applicationsData,
  getCurrentThemeColors,
  darkColor500,
  coreUrl,
  headerMenuData,
  isLoadingApplicationsManagementData,
  isLoadingShortApplications,
}) => {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const protocol = 'https:';

  const openHeaderMenuPopup = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const closeHeaderMenuPopup = () => {
    setAnchorEl(null);
  };

  const StyledLinkContent = withStyles({
    root: {
      display: 'flex',
      alignItems: 'center',
      width: '100%',
      height: '100%',
      color: darkColor500,
      paddingLeft: '24px',
      '&:hover': {
        color: getCurrentThemeColors(primaryColor)[500],
        backgroundColor: getCurrentThemeColors(primaryColor)[50],
        textDecoration: 'none',
      },
    },
  })(Typography);

  const StyledIconButton = withStyles({
    root: {
      '&:hover': {
        backgroundColor: getCurrentThemeColors(primaryColor)[50],
      },
    },
  })(IconButton);

  const getStyledLinkWrap = (linkIcon, linkTitle) => (
    <StyledLinkContent>
      <img src={linkIcon} className="switch-popup__link-icon" alt="" />
      <Typography variant="body2">{linkTitle}</Typography>
    </StyledLinkContent>
  );
  const AppIconButton = ({ title, iconUrl, bgColor }) => (
    <Box className="switch-popup__appIcon-wrapper">
      <Box className="icon-wrap" sx={{ backgroundColor: bgColor }}>
        <img
          src={iconUrl}
          alt={title}
          style={{ maxHeight: '100%', maxWidth: '100%', objectFit: 'contain' }}
        />
      </Box>
      <Typography className="icon-title" variant="body2">
        {title}
      </Typography>
    </Box>
  );
  AppIconButton.propTypes = {
    title: PropTypes.string,
    iconUrl: PropTypes.string,
    bgColor: PropTypes.string,
  };
  AppIconButton.defaultProps = {
    title: '',
    iconUrl: '',
    bgColor: '',
  };
  const getShortApplicationsContent = () => (
    applicationsData.map((application) => (
      <a
        href={application.url}
        key={application.title.toString()}
        target="_blank"
        onClick={closeHeaderMenuPopup}
        className="switch-popup__application-link"
        rel="noreferrer"
      >
        <AppIconButton
          title={application.title || ''}
          iconUrl={application.icon || ''}
          bgColor={application?.categories[0]?.color || '#2C41DC'}
        />
      </a>
    ))
  );

  const open = Boolean(anchorEl);
  const id = open ? 'header-switch-popover' : undefined;

  const preloader = (
    <CustomCircularProgress
      primaryColor={primaryColor}
      className="switch-popup__preloader"
      getCurrentThemeColors={getCurrentThemeColors}
    />
  );

  const applicationTitle = (
    <Typography variant="body1" component="span" className="switch-popup__title">
      Applications
    </Typography>
  );

  const getHeaderMenuLinks = () => (
    headerMenuData.map(({ title, url, icon }) => (
      url.includes(protocol) ? (
        <a href={url} key={title} className="switch-popup__link settings anchorTag">
          {getStyledLinkWrap(icon, title)}
        </a>
      ) : (
        <RouterLink to={url} key={title} className="switch-popup__link settings" onClick={closeHeaderMenuPopup}>
          {getStyledLinkWrap(icon, title)}
        </RouterLink>
      )
    ))
  );

  const getBaseRoute = () => (
    <>
      {isLoadingApplicationsManagementData ? preloader : getHeaderMenuLinks()}
      <RouterLink className="switch-popup__link settings" to="/" onClick={closeHeaderMenuPopup}>
        <StyledLinkContent>
          <AiOutlineHome size={24} className="switch-popup__link-icon" />
          <Typography variant="body2">Landing page</Typography>
        </StyledLinkContent>
      </RouterLink>
    </>
  );

  const getExternalRoute = (url) => (
    <>
      {getHeaderMenuLinks()}
      <a href={`${url}/`} className="switch-popup__link settings">
        <StyledLinkContent>
          <AiOutlineHome size={24} className="switch-popup__link-icon" />
          <Typography variant="body2">Landing page</Typography>
        </StyledLinkContent>
      </a>
    </>
  );

  return (
    <div>
      <StyledIconButton
        aria-describedby={id}
        onClick={openHeaderMenuPopup}
        className="header__menu-btn"
      >
        <GrApps size={24} />
      </StyledIconButton>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={closeHeaderMenuPopup}
        className="switch-popup"
      >
        <Typography variant="body1" component="span" className="switch-popup__title">Switch to:</Typography>
        {coreUrl ? getExternalRoute(coreUrl) : getBaseRoute()}
        <span className="switch-popup__separator" />
        {isLoadingShortApplications && (
          <>
            {applicationTitle}
            {preloader}
          </>
        )}
        {applicationsData.length ? applicationTitle : null}
        <Box sx={{ maxHeight: '300px', overflowY: 'auto' }}>
          {getShortApplicationsContent()}
        </Box>
      </Popover>
    </div>
  );
};

HeaderMenu.propTypes = {
  primaryColor: PropTypes.string.isRequired,
  applicationsData: PropTypes.instanceOf(Object),
  getCurrentThemeColors: PropTypes.func,
  darkColor500: PropTypes.string.isRequired,
  coreUrl: PropTypes.string.isRequired,
  headerMenuData: PropTypes.instanceOf(Array),
  isLoadingApplicationsManagementData: PropTypes.bool,
  isLoadingShortApplications: PropTypes.bool,
};

HeaderMenu.defaultProps = {
  applicationsData: [],
  headerMenuData: [],
  getCurrentThemeColors: (hex) => getBrandColors(hex),
  isLoadingApplicationsManagementData: false,
  isLoadingShortApplications: false,
};

export default HeaderMenu;
