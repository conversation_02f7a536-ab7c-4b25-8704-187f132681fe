.header__menu {
  &-btn {
    width: 40px;
    height: 40px;
    border-radius: 4px!important;
    padding: 0!important;
    margin-right: 10px!important;
    path {
      stroke: $dark-color-300;
    }
  }
}

.switch-popup {
  max-height: 620px;
  &__title {
    font-weight: 700!important;
    color: $dark-color-500;
    margin: 16px!important;
  }
  &__application-link {
    text-decoration: none;
  }
  &__appIcon-wrapper {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    .icon-wrap {
      height: 40px;
      width: 40px;
      display: flex;
      justify-content: center;
      padding: 4px;
      border-radius: 4px;
      flex: 0 0 40px;
    }
    .icon-title {
      margin-left: 10px;
      font-size: 12px;
      font-weight: 400;
      color: $dark-color-500;
      line-height: 1.4;
    }
  }
  &__link {
    display: flex;
    align-items: center;
    height: 50px;
    color: $dark-color-500;
    &.demo {
      padding-left: 16px;
    }
    &.settings {
      text-decoration: none;
      min-height: 50px;
    }
    &-icon {
      width: 24px;
      max-height: 24px;
      margin-right: 18px;
      color: $dark-color-300!important;
    }
  }
  &__separator {
    height: 1px;
    border-top: 1px solid $dark-color-50;
  }
  &__preloader {
    margin: 8px 0 20px 0;
  }
  .MuiPaper-elevation {
    display: flex;
    flex-direction: column;
    width: 300px;
    top: 60px!important;
    right: 10px!important;
    left: unset!important;
  }
  ::-webkit-scrollbar {
    width: 15px !important;
  }
  ::-webkit-scrollbar-track {
    background-color: transparent !important;
  }
  ::-webkit-scrollbar-thumb {
    background-color: $light-color-300 !important;
    border-radius: 30px !important;
    border: 6px solid transparent !important;
    background-clip: content-box !important;
  }
}
