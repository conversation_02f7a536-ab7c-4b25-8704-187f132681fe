import React from 'react';
import PropTypes from 'prop-types';
import Modal from '@mui/material/Modal';
import Fade from '@mui/material/Fade';
import {
  Typography,
  IconButton,
} from '@mui/material';
import { GrClose } from 'react-icons/gr';

import './ScheduleMeeting.scss';

const ScheduleMeeting = ({
  opened, handleOpen, container, hubspotMeetingUrl,
}) => (
  <Modal
    aria-labelledby="transition-modal-title"
    aria-describedby="transition-modal-description"
    open={opened}
    onClose={() => handleOpen(false)}
    container={container}
  >
    <Fade in={opened}>
      <div className="schedule-meeting-modal__wrap">
        <div className="schedule-meeting-modal">
          <Typography variant="h3" component="h3" className="schedule-meeting-modal__title">
            Schedule a meeting
          </Typography>
          <IconButton
            className="schedule-meeting-modal__btn-cancel"
            onClick={() => handleOpen(false)}
          >
            <GrClose size={24} />
          </IconButton>
          <div className="schedule-meeting-modal__embed-code">
            <iframe title="Schedule a meeting" src={hubspotMeetingUrl} />
          </div>
        </div>
      </div>
    </Fade>
  </Modal>
);

ScheduleMeeting.propTypes = {
  opened: PropTypes.bool,
  handleOpen: PropTypes.func,
  container: PropTypes.func,
  hubspotMeetingUrl: PropTypes.string.isRequired,
};

ScheduleMeeting.defaultProps = {
  opened: false,
  handleOpen: () => {},
  container: () => document.body,
};

export default ScheduleMeeting;
