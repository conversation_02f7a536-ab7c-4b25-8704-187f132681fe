import React from 'react';
import { render, screen } from '@testing-library/react';

import ScheduleMeeting from './ScheduleMeeting';

describe('ScheduleMeeting:', () => {
  test('should render ScheduleMeeting with "Schedule a meeting"', () => {
    const mockProps = {
      hubspotMeetingUrl: 'test_url',
      opened: true,
    };
    render(<ScheduleMeeting {...mockProps} />);

    expect(screen.getByText('Schedule a meeting')).toBeInTheDocument();
  });
});
