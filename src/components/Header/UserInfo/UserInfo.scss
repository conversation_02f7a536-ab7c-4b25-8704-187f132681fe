.user-info-popover {
  &__info-wrap {
    display: flex;
    flex-direction: column;
    padding: 16px;
    border-bottom: 1px solid $border-color;
  }
  &__name {
    font-weight: 700 !important;
    color: $dark-color-500;
    margin-bottom: 5px !important;
  }
  &__email {
    font-size: 12px !important;
    line-height: 16px;
    color: $dark-color-300;
  }
  &__logout-btn {
    justify-content: start !important;
    width: 100%;
    height: 50px;
    font-weight: 400 !important;
    padding: 0 !important;
    padding-left: 24px !important;
    color: $dark-color-500;
    text-transform: none !important;
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
    &:hover {
      cursor: pointer;
    }
  }
  &__logout-icon {
    margin-right: 16px;
    color: $dark-color-300;
  }
  &__logout-text {
    color: $dark-color-500;
  }
  &__preloader {
    margin: 25px 0 15px 0;
  }
  .MuiPaper-elevation {
    top: 60px !important;
    right: 10px !important;
    left: unset !important;
    min-width: 250px !important;
  }
}
