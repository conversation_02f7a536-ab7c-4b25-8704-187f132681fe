import React from 'react';
import PropTypes from 'prop-types';
import Dotdotdot from 'react-dotdotdot';

import {
  Popover, Typography, Button,
} from '@mui/material';
import Divider from '@mui/material/Divider';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import { BiLogIn } from 'react-icons/bi';
import CustomCircularProgress from '../../CustomCircularProgress';
import UserAvatar from '../../UserAvatar';

import './UserInfo.scss';

const UserInfo = ({
  signOut, userName, userEmail, primaryColor, getCurrentThemeColors, isLoadingUserInfo,
}) => {
  const [anchorEl, setAnchorEl] = React.useState(null);

  const openUserInfoPopup = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const closeUserInfoPopup = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'user-info-popover' : undefined;

  return (
    <>
      {isLoadingUserInfo
        ? (
          <CustomCircularProgress
            primaryColor={primaryColor}
            onClick={openUserInfoPopup}
            getCurrentThemeColors={getCurrentThemeColors}
          />
        )
        : (
          <UserAvatar
            describedId={id}
            onClick={openUserInfoPopup}
            userName={userName}
            cursor="pointer"
            getCurrentThemeColors={getCurrentThemeColors}
            primaryColor={primaryColor}
          />
        )}
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={closeUserInfoPopup}
        className="user-info-popover"
      >
        {isLoadingUserInfo ? (
          <>
            <CustomCircularProgress
              primaryColor={primaryColor}
              className="user-info-popover__preloader"
              getCurrentThemeColors={getCurrentThemeColors}
            />
            <Divider variant="middle" />
          </>
        )
          : (
            <div className="user-info-popover__info-wrap">
              <Typography variant="body2" component="span" className="user-info-popover__name">{userName}</Typography>
              <Typography variant="body2" component="span" className="user-info-popover__email">
                <Dotdotdot clamp={1}>
                  {userEmail}
                </Dotdotdot>
              </Typography>
            </div>
          )}
        <Button
          variant="text"
          color="primary"
          className="user-info-popover__logout-btn"
          startIcon={<BiLogIn size={24} className="user-info-popover__logout-icon" />}
          onClick={signOut}
        >
          <Typography variant="body2" component="span">Log out</Typography>
        </Button>
      </Popover>
    </>
  );
};

UserInfo.propTypes = {
  signOut: PropTypes.func.isRequired,
  userName: PropTypes.string.isRequired,
  userEmail: PropTypes.string.isRequired,
  primaryColor: PropTypes.string.isRequired,
  getCurrentThemeColors: PropTypes.func,
  isLoadingUserInfo: PropTypes.bool,
};

UserInfo.defaultProps = {
  getCurrentThemeColors: (hex) => getBrandColors(hex),
  isLoadingUserInfo: false,
};

export default UserInfo;
