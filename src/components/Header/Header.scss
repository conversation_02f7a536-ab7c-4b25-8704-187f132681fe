.header {
  position: fixed;
  top: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1200;
  width: 100%;
  height: 60px;
  background-color: $white-color;
  box-shadow: $shadow4;
  @media(max-width: $tablet-width) {
    z-index: 50;
  }
  @media(max-width: $mobile-width) {
    flex-wrap: wrap;
    height: auto;
    padding-top: 6px;
  }
  &__menu-wrap {
    display: flex;
    align-items: center;
    padding-right: 20px;
    @media(max-width: $mobile-width) {
      padding-right: 13px;
    }
  }
  &__menu-meeting-btn {
    margin-right: 15px!important;
    path {
      stroke: $dark-color-300;
    }
  }
  &__logo-wrap {
    display: flex;
    align-items: center;
  }
}
