import { useEffect } from 'react';
import { useMap } from 'react-leaflet';
import PropTypes from 'prop-types';

const RecenterMap = ({ lat, lng }) => {
  const map = useMap();

  useEffect(() => {
    map.setView([lat, lng]);
  }, [lat, lng, map]);

  return null;
};

RecenterMap.propTypes = {
  lat: PropTypes.number,
  lng: PropTypes.number,
};

RecenterMap.defaultProps = {
  lat: 51.505,
  lng: -0.09,
};

export default RecenterMap;
