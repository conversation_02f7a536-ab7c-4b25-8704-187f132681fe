/* eslint-disable global-require */
import React from 'react';
import { render, screen } from '@testing-library/react';
import {
  MapContainer, Circle,
} from 'react-leaflet';
import L from 'leaflet';
import ReactLeafletMapViewer from './index';

// Mock the react-leaflet components
jest.mock('react-leaflet', () => ({
  MapContainer: jest.fn(({ children }) => <div data-testid="map-container">{children}</div>),
  TileLayer: jest.fn(() => <div data-testid="tile-layer" />),
  Marker: jest.fn(({ children }) => <div data-testid="marker">{children}</div>),
  Popup: jest.fn(({ children }) => <div data-testid="popup">{children}</div>),
  Circle: jest.fn(() => <div data-testid="circle" />),
}));

// Mock the RecenterMap component
jest.mock('./RecenterMap', () => ({
  __esModule: true,
  default: jest.fn(() => <div data-testid="recenter-map" />),
}));

// Mock Leaflet
jest.mock('leaflet', () => {
  const originalModule = jest.requireActual('leaflet');
  return {
    ...originalModule,
    icon: jest.fn(() => ({})),
    Marker: {
      prototype: {
        options: {
          icon: null,
        },
      },
    },
  };
});

// Mock require for leaflet images
jest.mock('leaflet/dist/images/marker-icon.png', () => 'marker-icon.png');
jest.mock('leaflet/dist/images/marker-icon-2x.png', () => 'marker-icon-2x.png');
jest.mock('leaflet/dist/images/marker-shadow.png', () => 'marker-shadow.png');

describe('ReactLeafletMapViewer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with default props', () => {
    render(<ReactLeafletMapViewer />);

    expect(screen.getByTestId('map-container')).toBeInTheDocument();
    expect(screen.getByTestId('tile-layer')).toBeInTheDocument();
    expect(screen.getByTestId('recenter-map')).toBeInTheDocument();

    // Default props should not show marker or circle
    expect(screen.queryByTestId('marker')).not.toBeInTheDocument();
    expect(screen.queryByTestId('circle')).not.toBeInTheDocument();

    // Check if default icon is set
    expect(L.icon).toHaveBeenCalledWith(
      expect.objectContaining({
        iconAnchor: [12, 41],
        iconRetinaUrl: 'marker-shadow.png',
        iconSize: [25, 41],
        iconUrl: 'marker-shadow.png',
        popupAnchor: [1, -34],
        shadowSize: [41, 41],
        shadowUrl: 'marker-shadow.png',
      }),
    );
  });

  it('renders marker when showMarker is true', () => {
    render(<ReactLeafletMapViewer showMarker />);

    expect(screen.getByTestId('marker')).toBeInTheDocument();
    expect(screen.getByTestId('popup')).toBeInTheDocument();
  });

  it('renders circle when showCircle is true', () => {
    render(<ReactLeafletMapViewer showCircle />);

    expect(screen.getByTestId('circle')).toBeInTheDocument();
  });

  it('uses custom marker icon when markerIconUrl is provided', () => {
    const customIconUrl = 'custom-icon.png';
    const customIconSize = [40, 40];
    const customIconAnchor = [20, 40];
    const customPopupAnchor = [0, -40];

    render(
      <ReactLeafletMapViewer
        markerIconUrl={customIconUrl}
        iconSize={customIconSize}
        iconAnchor={customIconAnchor}
        popupAnchor={customPopupAnchor}
      />,
    );

    expect(L.icon).toHaveBeenCalledWith(expect.objectContaining({
      iconUrl: customIconUrl,
      iconRetinaUrl: customIconUrl,
      iconSize: customIconSize,
      iconAnchor: customIconAnchor,
      popupAnchor: customPopupAnchor,
      shadowUrl: null,
    }));
  });

  it('passes correct props to MapContainer', () => {
    const testLatitude = 40.7128;
    const testLongitude = -74.006;
    const testZoom = 10;

    render(
      <ReactLeafletMapViewer
        latitude={testLatitude}
        longitude={testLongitude}
        zoom={testZoom}
      />,
    );

    expect(MapContainer).toHaveBeenCalledWith(
      expect.objectContaining({
        center: [testLatitude, testLongitude],
        zoom: testZoom,
        scrollWheelZoom: true,
      }),
      expect.anything(),
    );
  });

  it('passes custom height and width to container div', () => {
    const testHeight = '600px';
    const testWidth = '800px';

    const { container } = render(
      <ReactLeafletMapViewer
        height={testHeight}
        width={testWidth}
      />,
    );

    const mapDiv = container.firstChild;
    expect(mapDiv).toHaveStyle(`height: ${testHeight}`);
    expect(mapDiv).toHaveStyle(`width: ${testWidth}`);
  });

  it('passes custom radius and options to Circle', () => {
    const testRadius = 2000;
    const testCircleOptions = {
      color: 'red',
      fillColor: 'pink',
      fillOpacity: 0.3,
    };

    render(
      <ReactLeafletMapViewer
        showCircle
        radius={testRadius}
        circleOptions={testCircleOptions}
      />,
    );

    expect(Circle).toHaveBeenCalledWith(
      expect.objectContaining({
        radius: testRadius,
        pathOptions: expect.objectContaining({
          color: 'red',
          fillColor: 'pink',
          fillOpacity: 0.3,
        }),
      }),
      expect.anything(),
    );
  });

  it('passes correct coordinates to RecenterMap', () => {
    const testLatitude = 35.6895;
    const testLongitude = 139.6917;

    render(
      <ReactLeafletMapViewer
        latitude={testLatitude}
        longitude={testLongitude}
      />,
    );

    const { default: RecenterMap } = require('./RecenterMap');
    expect(RecenterMap).toHaveBeenCalledWith(
      expect.objectContaining({
        lat: testLatitude,
        lng: testLongitude,
      }),
      expect.anything(),
    );
  });
});
