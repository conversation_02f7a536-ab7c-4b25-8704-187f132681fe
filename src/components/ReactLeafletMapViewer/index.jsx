/* eslint-disable global-require */
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  <PERSON><PERSON>ontaine<PERSON>,
  Tile<PERSON><PERSON>er,
  <PERSON>er,
  Popup,
  Circle,
} from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import RecenterMap from './RecenterMap';

const ReactLeafletMapViewer = ({
  latitude,
  longitude,
  data,
  title,
  zoom,
  height,
  width,
  radius,
  showMarker,
  showCircle,
  markerIconUrl,
  iconSize,
  iconAnchor,
  popupAnchor,
  circleOptions,
}) => {
  const position = [latitude, longitude];

  useEffect(() => {
    if (markerIconUrl) {
      const customIcon = L.icon({
        iconUrl: markerIconUrl,
        iconRetinaUrl: markerIconUrl,
        iconSize,
        iconAnchor,
        popupAnchor,
        shadowUrl: null,
      });

      L.Marker.prototype.options.icon = customIcon;
    } else {
      const defaultIcon = L.icon({
        iconUrl: require('leaflet/dist/images/marker-icon.png'),
        iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),
        shadowUrl: require('leaflet/dist/images/marker-shadow.png'),
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34],
        shadowSize: [41, 41],
      });

      L.Marker.prototype.options.icon = defaultIcon;
    }
  }, [markerIconUrl, iconSize, iconAnchor, popupAnchor]);

  return (
    <div style={{ height, width, position: 'relative' }}>
      <MapContainer
        center={position}
        zoom={zoom}
        scrollWheelZoom
        style={{ height: '100%', width: '100%', zIndex: 1 }}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        <RecenterMap lat={latitude} lng={longitude} />

        {showMarker && (
          <Marker position={position}>
            <Popup>
              <div>
                {title && (
                  <strong>
                    {title}
                    <br />
                    <hr />
                  </strong>
                )}
                {data && Object?.keys(data).length > 0 && (
                  <div>
                    {Object.entries(data).map(([key, value], i) => (
                      <React.Fragment key={key}>
                        <span>
                          {`${key}: `}
                          {value}
                          &nbsp;
                        </span>
                        {i % 2 === 1 && <br />}
                      </React.Fragment>
                    ))}
                  </div>
                )}
              </div>
            </Popup>
          </Marker>
        )}

        {showCircle && (
          <Circle
            center={position}
            radius={radius}
            pathOptions={{
              color: 'blue',
              fillColor: 'lightblue',
              fillOpacity: 0.6,
              ...circleOptions,
            }}
          />
        )}
      </MapContainer>
    </div>
  );
};

ReactLeafletMapViewer.propTypes = {
  latitude: PropTypes.number,
  longitude: PropTypes.number,
  data: PropTypes.instanceOf(Object),
  title: PropTypes.string,
  zoom: PropTypes.number,
  height: PropTypes.string,
  width: PropTypes.string,
  radius: PropTypes.number,
  showMarker: PropTypes.bool,
  showCircle: PropTypes.bool,
  markerIconUrl: PropTypes.string,
  iconSize: PropTypes.instanceOf(Array),
  iconAnchor: PropTypes.instanceOf(Array),
  popupAnchor: PropTypes.instanceOf(Array),
  circleOptions: PropTypes.instanceOf(Object),
};

ReactLeafletMapViewer.defaultProps = {
  latitude: 51.505,
  longitude: -0.09,
  data: {},
  title: '',
  zoom: 13,
  height: '400px',
  width: '100%',
  radius: 1000,
  showMarker: false,
  showCircle: false,
  markerIconUrl: null,
  iconSize: [30, 30],
  iconAnchor: [30, 30],
  popupAnchor: [30, 30],
  circleOptions: {},
};

export default ReactLeafletMapViewer;
