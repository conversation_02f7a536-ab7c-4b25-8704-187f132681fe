import React from 'react';
import PropTypes from 'prop-types';
import Modal from '@mui/material/Modal';
import Fade from '@mui/material/Fade';
import {
  Typography,
  Button,
} from '@mui/material';

import './RemoveEntityModal.scss';

const RemoveEntityModal = ({
  opened,
  handleOpen,
  title,
  description,
  container,
  removeEntity,
  entityContent,
}) => (
  <Modal
    className="remove-entity-modal__title"
    aria-labelledby="transition-modal-title"
    aria-describedby="transition-modal-description"
    open={opened}
    onClose={() => handleOpen(false)}
    container={container}
  >
    <Fade in={opened}>
      <div className="remove-entity-modal__wrap">
        <div className="remove-entity-modal">
          <Typography variant="h3" component="h3" className="remove-entity-modal__title">
            {title}
          </Typography>
          <Typography variant="body2" component="p" className="remove-entity-modal__desc">
            {description}
          </Typography>
          <div className="remove-entity-modal__entity-wrap">
            {entityContent}
          </div>
          <Button
            variant="contained"
            color="primary"
            className="remove-entity-modal__btn-remove"
            onClick={() => removeEntity()}
          >
            confirm
          </Button>
          <Button
            variant="outlined"
            color="primary"
            onClick={() => handleOpen(false)}
            className="remove-entity-modal__btn-decline"
          >
            decline
          </Button>
        </div>
      </div>
    </Fade>
  </Modal>
);

RemoveEntityModal.propTypes = {
  opened: PropTypes.bool,
  handleOpen: PropTypes.func,
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  removeEntity: PropTypes.func.isRequired,
  container: PropTypes.func,
  entityContent: PropTypes.element.isRequired,
};

RemoveEntityModal.defaultProps = {
  opened: false,
  handleOpen: () => {},
  container: () => document.body,
};

export default RemoveEntityModal;
