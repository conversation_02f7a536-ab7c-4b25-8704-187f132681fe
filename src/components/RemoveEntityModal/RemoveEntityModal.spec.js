import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';

import RemoveEntityModal from './RemoveEntityModal';

describe('RemoveEntityModal:', () => {
  test('should render RemoveEntityModal with "test title"', () => {
    const mockProps = {
      opened: true,
      title: 'test title',
      description: 'test description',
      removeEntity: () => {},
      entityContent: (<div />),
    };
    render(<RemoveEntityModal {...mockProps} />);

    expect(screen.getByText('test title')).toBeInTheDocument();
  });

  test('should call removeEntity', () => {
    const removeEntity = jest.fn();
    const mockProps = {
      opened: true,
      title: 'test title',
      description: 'test description',
      removeEntity,
      entityContent: (<div />),
    };
    const { getByText } = render(<RemoveEntityModal {...mockProps} />);
    const confirmBtn = getByText('confirm');

    fireEvent.click(confirmBtn);
    expect(removeEntity).toHaveBeenCalled();
  });
});
