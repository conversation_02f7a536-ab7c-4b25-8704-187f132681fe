import React from 'react';
import { NavLink } from 'react-router-dom';
import { Button } from '@mui/material';
import { AiOutlineLeft } from 'react-icons/ai';
import PropTypes from 'prop-types';

import backButtonModifier from './constants';

const BackButton = ({ path }) => (
  <NavLink
    to={path}
    className="back-button"
    data-testid="back-button"
  >
    <Button className="back-button__button" data-testid={backButtonModifier}>
      <AiOutlineLeft size="18" className="back-button__icon" />
    </Button>
  </NavLink>
);

BackButton.propTypes = {
  path: PropTypes.string.isRequired,
};

export default BackButton;
