import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import { unstable_HistoryRouter as HistoryRouter } from 'react-router-dom';
import { createMemoryHistory } from 'history';
import BackButton from './BackButton';
import backButtonModifier from './constants';

describe('BackButton:', () => {
  const mockPath = '/path';
  const route = '/list';

  const history = createMemoryHistory({ initialEntries: [route] });

  const backButton = (
    <HistoryRouter history={history}>
      <BackButton path={mockPath} />
    </HistoryRouter>
  );

  test('should redirect to mock path', () => {
    const { getByTestId } = render(backButton);

    const navLink = getByTestId(backButtonModifier);

    fireEvent.click(navLink);

    expect(history.location.pathname).toEqual(mockPath);
  });
});
