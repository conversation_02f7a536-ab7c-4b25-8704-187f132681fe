import React from 'react';
import { render, screen } from '@testing-library/react';
import CommonDrawer from './CommonDrawer';

describe('CommonDrawer Component', () => {
  const defaultProps = {
    className: 'drawer',
    anchor: 'left',
    onClose: jest.fn(),
    open: false,
    heading: 'Test Heading',
    variant: 'temporary',
    children: <div>Test Content</div>,
    headerClass: 'custom-class',
  };

  it('renders without crashing', () => {
    render(<CommonDrawer {...defaultProps} open />);
    const drawer = screen.getByTestId('drawer');
    expect(drawer).toBeInTheDocument();
  });

  it('renders with the correct className', () => {
    render(<CommonDrawer {...defaultProps} open />);
    const drawer = screen.getByTestId('drawer');
    expect(drawer).toHaveClass('drawer');
  });

  it('renders children correctly', () => {
    render(<CommonDrawer {...defaultProps} open />);
    const content = screen.getByText('Test Content');
    expect(content).toBeInTheDocument();
  });

  it('applies the correct variant prop', () => {
    render(<CommonDrawer {...defaultProps} variant="persistent" open />);
    const drawer = screen.getByTestId(defaultProps.className);
    expect(drawer).toHaveClass('MuiDrawer-docked');
  });

  it('does not render children when open is false', () => {
    render(<CommonDrawer {...defaultProps} open={false} />);
    const content = screen.queryByText('Test Content');
    expect(content).not.toBeInTheDocument();
  });
});
