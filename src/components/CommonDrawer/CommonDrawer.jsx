import React from 'react';
import { Drawer } from '@mui/material';
import { makeStyles } from '@mui/styles';
import PropTypes from 'prop-types';

const useStyles = makeStyles({
  drawerPaper: {
    width: 540,
  },
});

const CommonDrawer = ({
  className = 'drawer',
  children,
  anchor,
  onClose,
  open,
  variant,
}) => {
  const classes = useStyles();
  return (
    <Drawer
      className={className}
      anchor={anchor}
      open={open}
      variant={variant}
      onClose={onClose}
      classes={{
        paper: classes.drawerPaper,
      }}
      data-testid={className}
    >
      {children}
    </Drawer>
  );
};

CommonDrawer.propTypes = {
  className: PropTypes.string,
  anchor: PropTypes.oneOf(['left', 'right', 'top', 'bottom']),
  onClose: PropTypes.func,
  open: PropTypes.bool,
  variant: PropTypes.oneOf(['permanent', 'persistent', 'temporary']),
  children: PropTypes.node,
};

CommonDrawer.defaultProps = {
  className: 'drawer',
  anchor: 'left',
  onClose: () => { },
  open: false,
  variant: 'temporary',
  children: null,
};

export default CommonDrawer;
