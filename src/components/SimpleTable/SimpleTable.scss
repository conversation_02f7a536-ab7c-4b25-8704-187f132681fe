$scrollbarWidth:6px;

.simple-table {
  &__action {
    .MuiIconButton-root {
      height: 32px !important;
      width: 32px !important;
      border-radius: 4px;
      margin-right: 10px !important;
    }
    &:last-child {
      order: -1;
    }
  }

  &__actions {
    display: flex;
    @media(min-width: $mobile-width) {
      opacity: 0;
    }
  }

  .MuiTableRow-root {
    &[mode="update"] {
      .simple-table__actions {
        opacity: 1;
      }
    }
    &:hover {
      .simple-table__actions {
        opacity: 1;
      }
    }
  }

  &__toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;
    .MuiToolbar-regular {
      padding: 0!important;
      min-height: auto!important;
    }
    .MuiOutlinedInput-root {
      width: 250px;
      font-size: 12px!important;
    }
    .MuiInputAdornment-positionEnd {
      display: none!important;
    }
  }
  &__invite-btn {
    font-size: 12px!important;
  }
  &__failure-search {
    &-wrap {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative;
      left: -9px;
      width: calc(100% + 24px);
      padding-top: 100px;
      padding-bottom: 100px;
      background-color: $white-color;
      &::after {
        content: "";
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 1px;
        background-color: $white-color;
      }
      @media (max-width: $mobile-width) {
        padding-top: 40px;
        padding-bottom: 40px;
      }
    }
    &-icon-wrap {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 72px;
      height: 72px;
      border-radius: 4px;
    }
    &-title {
      margin-top: 16px!important;
      color: $dark-color-500!important;
    }
    &-text {
      margin-top: 5px!important;
      color: $dark-color-300!important;
      font-size: 14px!important;
    }
    &-btn {
      width: 125px;
      margin-top: 24px!important;
    }
  }

  &_update {
    .MuiCheckbox-root {
      cursor: default !important;
      &:hover {
        background-color: inherit !important;
      }
    }

    input[type="checkbox"] {
      pointer-events: none;
    }
  }

  tbody {
    .MuiTableCell-root {
      font-weight: 500!important;
    }
  }
  .MuiIconButton-root {
    padding: 0!important;
  }
  .MuiOutlinedInput-adornedStart {
    padding-left: 0;
  }
  .MuiOutlinedInput-input {
    padding-left: 8px !important;
  }
  .MuiTableCell-root {
    height: 48px;
    text-align: left !important;
  }
  &-mob {
    display: flex;
    flex-direction: column;
    &__toolbar {
      .MuiFormControl-root {
        width: 100%;
        margin-bottom: 20px;
      }
      .MuiButton-root {
        width: 100%;
      }
    }
    &__item {
      border-bottom: 1px solid $light-color-300;
      padding-top: 20px;
      padding-bottom: 20px;
      &:last-child {
        border: none;
        padding-bottom: 0;
      }
    }
    &__row {
      display: flex;
      align-items: center;
      margin-top: 20px;
      &:first-child {
        margin-top: 0;
      }
      &-name {
        max-width: 90px;
        width: 100%;
        margin-right: 20px !important;
      }
    }
  }
}

.user-management {
  &__actions-btn {
    visibility: hidden!important;
  }
  .MuiTableRow-root:hover {
    .user-management__actions-btn {
      visibility: visible!important;
    }
  }
}

.simple-table_scrolled {
  .simple-table__toolbar + div {
    & > div > div:after {
      content: "";
      position: absolute;
      bottom: 0;
      width: calc(100% - #{$scrollbarWidth});
      border-bottom: 1px solid $border-color;
    }
  }
  ::-webkit-scrollbar {
    width: $scrollbarWidth!important;
    background-color: transparent!important;
    -webkit-appearance: none;
  }
  ::-webkit-scrollbar-track {
    position: absolute!important;
    left: 0;
    background-color: transparent!important;
  }
  ::-webkit-scrollbar-thumb {
    right: 4px!important;
    position: absolute!important;
    border-width: 0!important;
    padding: 0!important;
    background-color: rgba(0, 0, 0, 0.2)!important;
    border-radius: 3px!important;
  }

  th:after,
  th:before {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
  }
  th:before {
    top: -1px;
    border-top: 1px solid $border-color;
  }
  th:after {
    bottom: -1px;
    border-bottom: 1px solid $border-color;
  }
}
