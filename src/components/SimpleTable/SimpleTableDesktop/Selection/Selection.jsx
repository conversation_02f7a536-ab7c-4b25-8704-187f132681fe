import React from 'react';
import PropTypes from 'prop-types';
import './Selection.scss';

const Selection = ({ data, selectedRows }) => (
  <div className="simple-table-toolbar-selection">
    <span>Selected</span>
    <div className="simple-table-toolbar-selection__amount">
      <b>
        {selectedRows.length}
        {' '}
      </b>
      of
      {' '}
      {data.length}
    </div>
  </div>
);

Selection.propTypes = {
  data: PropTypes.instanceOf(Array).isRequired,
  selectedRows: PropTypes.instanceOf(Array).isRequired,
};
export default Selection;
