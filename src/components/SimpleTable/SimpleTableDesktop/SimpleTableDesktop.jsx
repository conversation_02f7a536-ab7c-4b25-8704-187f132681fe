import React, {
  forwardRef, useCallback, useEffect, useState,
} from 'react';
import PropTypes from 'prop-types';
import MaterialTable, {
  MTableToolbar,
  MTableAction,
  MTableActions,
  MTableEditRow,
} from 'material-table';
import {
  AiOutlineCheckCircle,
  AiOutlineCloseCircle,
  AiOutlineSearch,
} from 'react-icons/ai';
import { GrEdit } from 'react-icons/gr';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

import IconCard from './IconCard';
import SimpleTableFailureSearch from '../SimpleTableFailureSearch';
import Selection from './Selection';

const SimpleTableDesktop = ({
  titles,
  data,
  isLoading,
  toolBarActionButtons,
  primaryColor,
  failureSearchData,
  searchProperty,
  maxBodyHeight,
  editable,
  actions,
  selection,
  getCurrentThemeColors,
  ...tableProps
}) => {
  const [isTableEditMode, setIsTableEditMode] = useState(false);
  const options = {
    search: !!searchProperty,
    showTitle: false,
    searchFieldAlignment: 'left',
    showFirstLastPageButtons: false,
    paging: false,
    sorting: false,
    draggable: false,
    showEmptyDataSourceMessage: true,
    debounceInterval: 1000,
    searchFieldVariant: 'outlined',
    actionsColumnIndex: -1,
    selection,
    showTextRowsSelected: false,
  };
  const tableIcons = {
    Search: forwardRef(useCallback(() => <AiOutlineSearch size={24} />)),
    Filter: () => '',
    Edit: forwardRef(
      useCallback((props, ref) => (
        <IconCard
          {...props}
          ref={ref}
          icon={(<GrEdit size={19} />)}
          primaryColor={primaryColor}
          getCurrentThemeColors={getCurrentThemeColors}
        />
      )),
    ),
    Check: forwardRef(
      useCallback((props, ref) => (
        <IconCard
          {...props}
          ref={ref}
          icon={(<AiOutlineCheckCircle size={19} />)}
          primaryColor={primaryColor}
          getCurrentThemeColors={getCurrentThemeColors}
        />
      )),
    ),
    Clear: React.forwardRef(
      useCallback((props, ref) => (
        <IconCard
          {...props}
          ref={ref}
          icon={(<AiOutlineCloseCircle size={19} />)}
          primaryColor={primaryColor}
          getCurrentThemeColors={getCurrentThemeColors}
        />
      )),
    ),

  };
  const localization = {
    body: {
      emptyDataSourceMessage: <SimpleTableFailureSearch
        primaryColor={primaryColor}
        failureSearchData={failureSearchData}
        getCurrentThemeColors={getCurrentThemeColors}
      />,
      editRow: {
        saveTooltip: 'Confirm',
      },
    },
  };

  const Toolbar = useCallback((props) => (
    <div className="simple-table__toolbar">
      <MTableToolbar {...props} />
      {selection && <Selection {...props} />}
      {toolBarActionButtons}
    </div>
  ));

  const Action = useCallback((props) => (
    <div className="simple-table__action">
      <MTableAction {...props} />
    </div>
  ));

  const Actions = useCallback((props) => (
    <div className="simple-table__actions" data-id={props.data?.id}>
      <MTableActions {...props} />
    </div>
  ));

  const EditRow = useCallback((props) => {
    useEffect(() => {
      setIsTableEditMode(true);

      return () => setIsTableEditMode(false);
    }, []);

    return (
      <MTableEditRow
        {...props}
        sx={{ backgroundColor: getCurrentThemeColors(primaryColor)[50] }}
      />
    );
  });

  const isNeedToScroll = maxBodyHeight && data.length;
  const materialTableOptions = isNeedToScroll ? { ...options, maxBodyHeight } : options;
  const scrollClassName = isNeedToScroll ? 'simple-table_scrolled' : '';
  const editableModeClassName = isTableEditMode ? 'simple-table_update' : '';

  return (
    <div className={`simple-table ${scrollClassName} ${editableModeClassName}`}>
      <MaterialTable
        style={{
          boxShadow: 'none',
        }}
        icons={tableIcons}
        columns={titles}
        options={materialTableOptions}
        data={data}
        isLoading={isLoading}
        localization={localization}
        components={{
          Toolbar, Actions, Action, EditRow,
        }}
        actions={actions}
        editable={editable}
        {...tableProps}
      />
    </div>
  );
};

SimpleTableDesktop.propTypes = {
  titles: PropTypes.instanceOf(Array).isRequired,
  data: PropTypes.instanceOf(Array),
  isLoading: PropTypes.bool,
  toolBarActionButtons: PropTypes.node,
  primaryColor: PropTypes.string.isRequired,
  failureSearchData: PropTypes.instanceOf(Object),
  searchProperty: PropTypes.string,
  maxBodyHeight: PropTypes.number.isRequired,
  editable: PropTypes.instanceOf(Object),
  actions: PropTypes.instanceOf(Array),
  selection: PropTypes.bool,
  getCurrentThemeColors: PropTypes.func,
};

SimpleTableDesktop.defaultProps = {
  data: [],
  isLoading: false,
  toolBarActionButtons: '',
  failureSearchData: {},
  searchProperty: '',
  editable: {},
  actions: [],
  selection: false,
  getCurrentThemeColors: (hex) => getBrandColors(hex),
};

export default SimpleTableDesktop;
