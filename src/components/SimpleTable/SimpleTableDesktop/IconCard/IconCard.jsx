import React from 'react';
import PropTypes from 'prop-types';

import { Typography } from '@mui/material';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

import './IconCard.scss';

const IconCard = ({
  primaryColor, icon, getCurrentThemeColors, ...props
}) => (
  <Typography
    className="icon-card"
    component="span"
    sx={{
      '&:hover': { backgroundColor: getCurrentThemeColors(primaryColor)[100] },
      '&:hover svg path': {
        stroke: `${primaryColor} !important`,
        color: `${primaryColor} !important`,
      },
    }}
    {...props}
  >
    {icon}
  </Typography>
);

IconCard.propTypes = {
  primaryColor: PropTypes.string,
  icon: PropTypes.element.isRequired,
  getCurrentThemeColors: PropTypes.func,
};

IconCard.defaultProps = {
  primaryColor: '',
  getCurrentThemeColors: (hex) => getBrandColors(hex),
};

export default IconCard;
