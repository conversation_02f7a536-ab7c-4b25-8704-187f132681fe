import React from 'react';
import PropTypes from 'prop-types';
import { Typography } from '@mui/material';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

const SimpleTableFailureSearch = ({ failureSearchData, primaryColor, getCurrentThemeColors }) => (
  <div className="simple-table__failure-search-wrap">
    <div
      className="simple-table__failure-search-icon-wrap"
      style={{ backgroundColor: getCurrentThemeColors(primaryColor)[50] }}
    >
      {failureSearchData.icon}
    </div>
    <Typography variant="h3" component="h3" className="simple-table__failure-search-title">
      {failureSearchData.title}
    </Typography>
    <Typography variant="body1" className="simple-table__failure-search-text">
      {failureSearchData.description}
    </Typography>
    {failureSearchData.additionalItem}
  </div>
);

SimpleTableFailureSearch.propTypes = {
  failureSearchData: PropTypes.instanceOf(Object),
  primaryColor: PropTypes.string.isRequired,
  getCurrentThemeColors: PropTypes.func,
};

SimpleTableFailureSearch.defaultProps = {
  failureSearchData: {},
  getCurrentThemeColors: (hex) => getBrandColors(hex),
};

export default SimpleTableFailureSearch;
