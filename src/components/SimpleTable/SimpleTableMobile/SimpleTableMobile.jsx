import React, { useState } from 'react';
import PropTypes from 'prop-types';

import {
  FormControl, InputAdornment, InputLabel, OutlinedInput,
} from '@mui/material';
import { AiOutlineSearch } from 'react-icons/ai';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

import SimpleTableMobilePreloader from './SimpleTableMobilePreloader';
import SimpleTableMobileRow from './SimpleTableMobileRow';
import SimpleTableFailureSearch from '../SimpleTableFailureSearch';

const SimpleTableMobile = ({
  data,
  titles,
  inviteButton,
  isLoading,
  searchProperty,
  primaryColor,
  failureSearchData,
  maxBodyHeight,
  getCurrentThemeColors,
}) => {
  const [searchValue, setSearchValue] = useState('');

  const handleSearch = (e) => {
    const searchedValue = e.target.value.toLowerCase();

    setSearchValue(searchedValue);
  };

  const getTableMobRow = (itemsData) => (
    itemsData.length
      ? (
        <div style={{ maxHeight: maxBodyHeight, overflowY: 'auto' }}>
          {itemsData.map((itemData, index) => (
            <div className="simple-table-mob__item">
              <SimpleTableMobileRow
                  /* eslint-disable-next-line react/no-array-index-key */
                key={index.toString()}
                itemsTitles={titles}
                itemRowData={itemData}
              />
            </div>
          ))}
        </div>
      )
      : (
        <SimpleTableFailureSearch
          primaryColor={primaryColor}
          failureSearchData={failureSearchData}
          getCurrentThemeColors={getCurrentThemeColors}
        />
      )
  );

  const getSearchTableMobRow = () => {
    const searchData = [];

    data.forEach((item) => {
      const isIncludeSearchValue = item[searchProperty].toLowerCase()
        .includes(searchValue.toLowerCase());

      if (isIncludeSearchValue) {
        searchData.push(item);
      }
    });

    return searchData.length
      ? getTableMobRow(searchData)
      : (
        <SimpleTableFailureSearch
          primaryColor={primaryColor}
          failureSearchData={failureSearchData}
          getCurrentThemeColors={getCurrentThemeColors}
        />
      );
  };

  const tableMobContent = (
    searchValue ? getSearchTableMobRow() : getTableMobRow(data)
  );

  return (
    <div className="simple-table-mob simple-table_scrolled">
      <div className="simple-table-mob__toolbar">
        {searchProperty ? (
          <FormControl variant="outlined">
            <InputLabel htmlFor="search">Search</InputLabel>
            <OutlinedInput
              id="search"
              label="Search"
              value={searchValue}
              type="search"
              onChange={handleSearch}
              endAdornment={(
                <InputAdornment position="start">
                  <AiOutlineSearch size={24} />
                </InputAdornment>
            )}
            />
          </FormControl>
        ) : ''}
        {inviteButton}
      </div>
      {isLoading ? <SimpleTableMobilePreloader /> : tableMobContent}
    </div>
  );
};

SimpleTableMobile.propTypes = {
  data: PropTypes.instanceOf(Array),
  titles: PropTypes.instanceOf(Array).isRequired,
  inviteButton: PropTypes.node,
  isLoading: PropTypes.bool,
  searchProperty: PropTypes.string,
  primaryColor: PropTypes.string.isRequired,
  failureSearchData: PropTypes.instanceOf(Object),
  maxBodyHeight: PropTypes.number.isRequired,
  getCurrentThemeColors: PropTypes.func,
};

SimpleTableMobile.defaultProps = {
  data: [],
  inviteButton: '',
  isLoading: false,
  searchProperty: '',
  failureSearchData: {},
  getCurrentThemeColors: (hex) => getBrandColors(hex),
};

export default SimpleTableMobile;
