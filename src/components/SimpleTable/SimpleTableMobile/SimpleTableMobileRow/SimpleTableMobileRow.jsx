import React from 'react';
import PropTypes from 'prop-types';
import { Typography } from '@mui/material';

const SimpleTableMobileRow = ({ itemsTitles, itemRowData }) => (
  itemsTitles.map(({ title, render }) => (
    <div className="simple-table-mob__row" key={title.toString()}>
      <Typography variant="body2" component="span" className="simple-table-mob__row-name">{title}</Typography>
      <Typography variant="body2" component="span" className="bold">
        {render(itemRowData)}
      </Typography>
    </div>
  ))
);

SimpleTableMobileRow.propTypes = {
  itemsTitles: PropTypes.instanceOf(Array).isRequired,
  itemRowData: PropTypes.instanceOf(Object).isRequired,
};

export default SimpleTableMobileRow;
