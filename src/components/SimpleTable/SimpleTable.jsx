import React from 'react';
import PropTypes from 'prop-types';
import { useMediaQuery } from '@mui/material';

import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';

import SimpleTableDesktop from './SimpleTableDesktop';
import SimpleTableMobile from './SimpleTableMobile';

import './SimpleTable.scss';

const SimpleTable = (
  {
    data,
    isLoading,
    titles,
    inviteButton,
    searchProperty,
    primaryColor,
    failureSearchData,
    maxBodyHeight,
    editable,
    actions,
    getCurrentThemeColors,
    isNeedDefaultTableMobile,
    ...props
  },
) => {
  const isMobile = useMediaQuery(`(max-width: ${styles.mobileWidth})`);

  return isMobile && isNeedDefaultTableMobile
    ? (
      <SimpleTableMobile
        data={data}
        titles={titles}
        inviteButton={inviteButton}
        isLoading={isLoading}
        searchProperty={searchProperty}
        primaryColor={primaryColor}
        failureSearchData={failureSearchData}
        maxBodyHeight={maxBodyHeight}
        getCurrentThemeColors={getCurrentThemeColors}
      />
    )
    : (
      <SimpleTableDesktop
        titles={titles}
        data={data}
        isLoading={isLoading}
        inviteButton={inviteButton}
        primaryColor={primaryColor}
        failureSearchData={failureSearchData}
        searchProperty={searchProperty}
        maxBodyHeight={maxBodyHeight}
        editable={editable}
        actions={actions}
        getCurrentThemeColors={getCurrentThemeColors}
        {...props}
      />
    );
};

SimpleTable.propTypes = {
  data: PropTypes.instanceOf(Array),
  titles: PropTypes.instanceOf(Array).isRequired,
  inviteButton: PropTypes.node,
  isLoading: PropTypes.bool,
  searchProperty: PropTypes.string,
  primaryColor: PropTypes.string.isRequired,
  failureSearchData: PropTypes.instanceOf(Object),
  maxBodyHeight: PropTypes.number,
  editable: PropTypes.instanceOf(Object),
  actions: PropTypes.instanceOf(Array),
  getCurrentThemeColors: PropTypes.func,
  isNeedDefaultTableMobile: PropTypes.bool,
};

SimpleTable.defaultProps = {
  data: [],
  inviteButton: '',
  isLoading: false,
  searchProperty: '',
  failureSearchData: {},
  maxBodyHeight: null,
  editable: {},
  actions: [],
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
  isNeedDefaultTableMobile: true,
};

export default SimpleTable;
