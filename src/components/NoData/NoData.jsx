import React from 'react';
import PropTypes from 'prop-types';
import { Typography } from '@mui/material';
import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import noDataWrapperModifier from './constants';

const NoData = ({
  icon, title, description, additionalItem, getCurrentThemeColors, themeName,
}) => (
  <div className="no-data__wrap" data-testid={noDataWrapperModifier}>
    <div
      className="no-data__icon-wrap"
      style={{ backgroundColor: getCurrentThemeColors(themeConfig[themeName].primaryColor)[50] }}
    >
      {icon}
    </div>
    <Typography variant="h3" component="h3" className="no-data__title">
      {title}
    </Typography>
    <Typography variant="body1" className="no-data__text">
      {description}
    </Typography>
    {additionalItem}
  </div>
);

NoData.propTypes = {
  icon: PropTypes.instanceOf(Object).isRequired,
  title: PropTypes.string,
  description: PropTypes.string,
  additionalItem: PropTypes.instanceOf(Object),
  getCurrentThemeColors: PropTypes.func,
  themeName: PropTypes.string,
};

NoData.defaultProps = {
  title: 'No data',
  description: '',
  additionalItem: null,
  getCurrentThemeColors: (hex) => getBrandColors(hex),
  themeName: 'bt',
};

export default NoData;
