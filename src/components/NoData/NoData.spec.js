import React from 'react';

import { render } from '@testing-library/react';
import { AiOutlineDollar } from 'react-icons/ai';

import NoData from './NoData';
import noDataWrapperModifier from './constants';

describe('NoData:', () => {
  const testProps = {
    icon: <AiOutlineDollar />,
    title: 'No budgets',
  };
  const getNoData = (
    <NoData {...testProps} />
  );

  test('should be "no data wrapper" in the DOM', () => {
    const { getByTestId } = render(getNoData);

    const noDataWrapper = getByTestId(noDataWrapperModifier);

    expect(noDataWrapper).toBeInTheDocument();
  });

  test('should be text "No budgets" in the DOM', () => {
    const { getByText } = render(getNoData);

    const title = getByText(testProps.title);

    expect(title).toBeInTheDocument();
  });

  test('should be default title "No data" in the DOM', () => {
    const defaultTitle = 'No data';

    const { getByText } = render(
      <NoData icon={testProps.icon} />,
    );
    const title = getByText(defaultTitle);

    expect(title).toBeInTheDocument();
  });
});
