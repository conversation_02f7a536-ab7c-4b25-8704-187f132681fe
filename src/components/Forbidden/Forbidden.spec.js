import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import { unstable_HistoryRouter as HistoryRouter } from 'react-router-dom';
import { createMemoryHistory } from 'history';

import Forbidden from './Forbidden';

describe('Forbidden:', () => {
  const route = '/test';
  const homeRoute = '/';
  const history = createMemoryHistory({ initialEntries: [route] });
  const getCurrentThemeColors = () => ({
    100: '',
    200: '',
  });

  const mockProps = {
    primaryColor: '#000000',
    getCurrentThemeColors,
  };

  const forbidden = (
    <HistoryRouter history={history}>
      <Forbidden {...mockProps} />
    </HistoryRouter>
  );
  test('should be redirect button', () => {
    const { getByTestId } = render(forbidden);
    const btn = getByTestId('Forbidden__btn');

    expect(btn).toBeInTheDocument();
  });

  test('should redirects to landing(home)', () => {
    const { getByTestId } = render(forbidden);
    const btn = getByTestId('Forbidden__btn');

    fireEvent.click(btn);

    expect(history.location.pathname).toEqual(homeRoute);
  });
});
