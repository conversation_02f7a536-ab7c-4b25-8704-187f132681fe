import React from 'react';

import { Button, Typography } from '@mui/material';
import { Link as RouteLink } from 'react-router-dom';

import ForbiddenIcon from './ForbiddenIcon';

interface IForbiddenProps {
  primaryColor: string
  getCurrentThemeColors: (color: string) => { [key: string]: string }
  secondaryColor?: string | null
}

const Forbidden = ({
  getCurrentThemeColors, primaryColor, secondaryColor,
}: IForbiddenProps) => (
  // for bt primaryColor is secondaryColor; secondaryColor is primaryColor
  <div className="Forbidden__wrap">
    <div className="Forbidden">
      <ForbiddenIcon
        primaryColor={primaryColor}
        getCurrentThemeColors={getCurrentThemeColors}
        secondaryColor={secondaryColor}
      />
      <Typography
        variant="h2"
        color={getCurrentThemeColors(primaryColor)[100]}
        component="h2"
        className="Forbidden__error"
      >
        403
      </Typography>
      <Typography variant="h3" component="h4" className="Forbidden__title">Forbidden</Typography>
      <Typography variant="body1" component="p" className="Forbidden__text">
        You do not have sufficient rights to a resource.
      </Typography>
      <Button
        component={RouteLink}
        variant="contained"
        sx={{
          backgroundColor: secondaryColor || primaryColor,
        }}
        className="Forbidden__btn"
        data-testid="Forbidden__btn"
        to="/"
      >
        Go to the Homepage
      </Button>
    </div>
  </div>
);

Forbidden.defaultProps = {
  secondaryColor: null,
};

export default Forbidden;
