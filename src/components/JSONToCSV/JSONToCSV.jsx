import React from 'react';
import {
  element, func, string,
} from 'prop-types';
import { GridExport } from './images/ImagePlaceholder';
import convertJsonToCsv from './utilities/convertJsonToCsv';
import ActionButtonWithTooltip from './ActionButtonWithTooltip';

const JSONToCSV = ({
  getData,
  ...props
}) => {
  const generateCSVFile = () => {
    try {
      const data = getData();
      const csvData = convertJsonToCsv(data, { ...props }?.delimiter);
      const csvBlob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
      const csvUrl = URL.createObjectURL(csvBlob);
      const downloadLink = document.createElement('a');
      downloadLink.href = csvUrl;
      downloadLink.download = `${{ ...props }?.fileName}.csv`;
      downloadLink.style.display = 'none';
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(csvUrl);
      // eslint-disable-next-line no-console
      console.info('Generate Data');
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('Error generating CSV:', err);
    }
  };

  return (
    <ActionButtonWithTooltip
      action={() => generateCSVFile()}
      {...props}
    />
  );
};

JSONToCSV.propTypes = {
  getData: func,
  title: string,
  icon: element,
  delimiter: string,
  fileName: string,
};

JSONToCSV.defaultProps = {
  getData: null,
  title: 'Tooltip',
  icon: <GridExport />,
  delimiter: ',',
  fileName: 'BackGround',
};

export default JSONToCSV;
