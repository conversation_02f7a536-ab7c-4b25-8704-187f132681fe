import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react';
import convertJsonToCsv from 'core/utilities/convertJsonToCsv';
import JSONToCSV from './JSONToCSV';

// Mock the convertJsonToCsv function
jest.mock('core/utilities/convertJsonToCsv', () => jest.fn().mockImplementation(
  () => 'Name,Age,Email\nJohn <PERSON>,30,<EMAIL>\nJ<PERSON>,25,<EMAIL>',
));

// Mock the Blob and URL.createObjectURL
global.Blob = jest.fn();
global.URL.createObjectURL = jest.fn().mockImplementation(() => 'fake-csv-url');

describe('JSONToCSV', () => {
  const jsonData = [
    { name: '<PERSON>', age: 30, email: '<EMAIL>' },
    { name: '<PERSON>', age: 25, email: '<EMAIL>' },
  ];

  it('renders without crashing', () => {
    render(<JSONToCSV data={jsonData} />);
  });

  it('generates CSV file when the action button is clicked', () => {
    const { getByTestId } = render(<JSONToCSV data={jsonData} />);
    const actionButton = getByTestId('button-with-tooltip-icon'); // Replace with your actual action button text

    fireEvent.click(actionButton);
    waitFor(() => {
    // Assert that the convertJsonToCsv function is called with the correct data
      expect(convertJsonToCsv).toHaveBeenCalledWith(jsonData, undefined);

      // Assert that the Blob and URL.createObjectURL are called with the correct data
      expect(Blob).toHaveBeenCalledWith(['Name,Age,Email\nJohn Doe,30,<EMAIL>\nJane Smith,25,<EMAIL>'], {
        type: 'text/csv;charset=utf-8;',
      });
      expect(URL.createObjectURL).toHaveBeenCalledWith(new Blob());

    // Since we mocked URL.createObjectURL, we don't need to actually test the download behavior
    // You can add more specific assertions if you have additional behaviors to test.
    });
  });

  it('handles error when generating CSV', () => {
    // Mock the console.error function to avoid printing errors in the test output.
    jest.spyOn(console, 'error').mockImplementation(() => {});

    // Mock the convertJsonToCsv function to throw an error.
    convertJsonToCsv.mockImplementation(() => {
      throw new Error('Error generating CSV');
    });

    const { getByTestId } = render(<JSONToCSV data={jsonData} />);
    const actionButton = getByTestId('button-with-tooltip-icon'); // Replace with your actual action button text
    waitFor(() => {
      fireEvent.click(actionButton);

      // Assert that the convertJsonToCsv function is called with the correct data
      expect(convertJsonToCsv).toHaveBeenCalledWith(jsonData, undefined);

      // Assert that the console.error is called with the error message.
      // eslint-disable-next-line no-console
      expect(console.error).toHaveBeenCalledWith('Error generating CSV', expect.any(Error));

      // Restore the original console.error and convertJsonToCsv functions.
      // eslint-disable-next-line no-console
      console.error.mockRestore();
      convertJsonToCsv.mockRestore();
    });
  });
});
