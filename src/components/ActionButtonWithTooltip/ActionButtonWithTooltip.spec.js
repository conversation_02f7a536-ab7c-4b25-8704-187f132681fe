import React from 'react';
import { render, screen } from '@testing-library/react';
import { GrAchievement } from 'react-icons/gr';
import userEvent from '@testing-library/user-event';
import ActionButtonWithTooltip from './ActionButtonWithTooltip';

const mockProps = {
  title: 'test tooltip',
  icon: <GrAchievement />,
};

describe('ActionButtonWithTooltip:', () => {
  test('should render ActionButtonWithTooltip with "test tooltip"', () => {
    render(<ActionButtonWithTooltip {...mockProps} />);

    expect(screen.getByLabelText('test tooltip')).toBeInTheDocument();
  });

  test('should show tooltip when user hover on button', () => {
    render(<ActionButtonWithTooltip {...mockProps} />);

    const button = screen.getByTestId('button-with-tooltip-icon');

    userEvent.hover(button);

    expect(screen.getByText('test tooltip')).toBeInTheDocument();
  });
});
