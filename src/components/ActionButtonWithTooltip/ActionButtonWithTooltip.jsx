import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { IconButton, Tooltip } from '@mui/material';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

const ActionButtonWithTooltip = ({
  title, className, action, icon, placement, primaryColor, disabled, getCurrentThemeColors, ...props
}) => {
  const [isShown, setIsShown] = useState(false);

  const styles = primaryColor ? {
    backgroundColor: `${getCurrentThemeColors(primaryColor)[50]} !important`,
    '&:hover': {
      backgroundColor: `${getCurrentThemeColors(primaryColor)[100]} !important`,
    },
  } : {};

  const clickAction = (e) => {
    action(e);
    setIsShown(false);
  };

  return (
    <Tooltip
      title={title}
      placement={placement}
      open={isShown}
      arrow
      disableHoverListener={disabled}
    >
      <IconButton
        className={className}
        onMouseEnter={() => setIsShown(true)}
        onMouseLeave={() => setIsShown(false)}
        onClick={clickAction}
        disabled={disabled}
        data-testid="button-with-tooltip-icon"
        sx={{ ...styles, ...props }}
      >
        {icon}
      </IconButton>
    </Tooltip>
  );
};

ActionButtonWithTooltip.propTypes = {
  title: PropTypes.string.isRequired,
  className: PropTypes.string,
  action: PropTypes.func,
  icon: PropTypes.element.isRequired,
  placement: PropTypes.string,
  primaryColor: PropTypes.string,
  disabled: PropTypes.bool,
  getCurrentThemeColors: PropTypes.func,

};

ActionButtonWithTooltip.defaultProps = {
  className: '',
  action: () => {},
  placement: 'top',
  primaryColor: '',
  disabled: false,
  getCurrentThemeColors: (hex) => getBrandColors(hex),
};

export default ActionButtonWithTooltip;
