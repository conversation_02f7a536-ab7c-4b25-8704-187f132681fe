import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import IframeViewer from './IframeViewer';

describe('IframeViewer', () => {
  const defaultSrc = 'https://example.com/';

  test('should render iframe with provided src and title', () => {
    render(<IframeViewer src={defaultSrc} title="Example Frame" />);
    const iframe = screen.getByTitle('Example Frame') as HTMLIFrameElement;

    expect(iframe).toBeInTheDocument();
    expect(iframe.src).toBe(defaultSrc);
  });

  test('should not render Loader if showLoading is false', () => {
    render(<IframeViewer src={defaultSrc} title="No Loader Frame" showLoading={false} />);
    expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
  });

  test('should call onLoad and hide loader when iframe loads', () => {
    const onLoadMock = jest.fn();

    render(<IframeViewer src={defaultSrc} title="Load Frame" onLoad={onLoadMock} />);
    const iframe = screen.getByTitle('Load Frame');

    fireEvent.load(iframe);

    expect(onLoadMock).toHaveBeenCalled();
    expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
  });

  test('should apply default sandbox if none provided', () => {
    render(<IframeViewer src={defaultSrc} title="Sandbox Frame" />);
    const iframe = screen.getByTitle('Sandbox Frame');

    expect(iframe.getAttribute('sandbox')).toBe('allow-same-origin allow-scripts allow-forms allow-popups');
  });

  test('should allow overriding sandbox value via props', () => {
    const customSandbox = 'allow-scripts';

    render(
      <IframeViewer
        src={defaultSrc}
        title="Custom Sandbox"
        sandbox={customSandbox}
      />,
    );
    const iframe = screen.getByTitle('Custom Sandbox');

    expect(iframe.getAttribute('sandbox')).toBe(customSandbox);
  });

  test('should apply additional props to iframe (like name, data attributes)', () => {
    render(
      <IframeViewer
        src={defaultSrc}
        title="Extra Props"
        name="iframe-name"
        data-testid="custom-iframe"
      />,
    );

    const iframe = screen.getByTestId('custom-iframe');

    expect(iframe).toHaveAttribute('name', 'iframe-name');
    expect(iframe).toHaveAttribute('src', defaultSrc);
  });

  test('should use default theme name when not provided', () => {
    render(<IframeViewer src={defaultSrc} title="Default Theme" />);

    expect(screen.getByTitle('Default Theme')).toBeInTheDocument();
  });

  test('should accept custom theme name', () => {
    render(<IframeViewer src={defaultSrc} title="Custom Theme" themeName="customTheme" />);

    expect(screen.getByTitle('Custom Theme')).toBeInTheDocument();
  });

  test('should handle theme loading failures gracefully', () => {
    render(<IframeViewer src={defaultSrc} title="Fallback Test" />);

    expect(screen.getByTitle('Fallback Test')).toBeInTheDocument();
    expect(screen.getByTestId('loader')).toBeInTheDocument();
  });
});
