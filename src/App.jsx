// App.jsx is used to test components for local development

import React, { useState } from 'react';
import { BrowserRouter, Route, Routes } from 'react-router-dom';

import { ThemeProvider } from '@mui/material/styles';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';

import TNSLogo from 'assets/images/ts-nextgen-header-logo.svg';
import TNSLogoMob from 'assets/images/ts-nextgen-header-logo-mob.svg';
import breadcrumbsConfig from 'core/configs/breadcrumbsConfig';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import { AiOutlineSetting, AiOutlineUsergroupAdd } from 'react-icons/ai';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';
import Sidebar from './components/Sidebar';

import './App.scss';

import Header from './components/Header';
import SimpleTable from './components/SimpleTable';
import MuiTable from './components/MuiTable';
import { MuiTableProvider } from './components/MuiTable/MuiTableContext';

const userName = 'Test';
const userEmail = '<EMAIL>';
const signOut = () => {
};

const currentTheme = themeConfig.bt;
const primaryColor = styles.brandBlueColor500;
const secondaryColor = styles.yellowColor500;

const App = () => {
  const [toggled, setToggled] = useState(false);
  const [collapsed, setCollapsed] = useState(true);

  const handleToggleSidebar = () => {
    setToggled(!toggled);
  };

  const handleToggleCollapsed = (mobFlag) => {
    if (mobFlag === 'mob') {
      setCollapsed(false);
    } else {
      setCollapsed(!collapsed);
    }
  };

  const sidebarConfig = [
    {
      id: 'user-management',
      name: 'User management',
      path: '/administration/user-management',
      icon: <AiOutlineUsergroupAdd size={24} className="sidebar__nav-link-icon" />,
    },
    {
      id: 'settings',
      name: 'Settings',
      path: '/administration/settings',
      icon: <AiOutlineSetting size={24} className="sidebar__nav-link-icon" />,
    },
  ];

  const simpleTableData = [
    {
      client_hub_pmn_code: 'AAA',
      client_operator_pmn_code: 'BBB',
      partner_pmn_code: 'CCC',
      generated_at: 'DDD',
    },
  ];

  const simpleTableTitles = [
    {
      title: 'Client Hub PMN',
      field: 'client_hub_pmn_code',
      render: (rowData) => rowData.client_hub_pmn_code,
    },
    {
      title: 'Client Operator PMN',
      field: 'client_operator_pmn_code',
      render: (rowData) => rowData.client_operator_pmn_code,
    },
    {
      title: 'Partner PMN',
      field: 'partner_pmn_code',
      render: (rowData) => rowData.partner_pmn_code,
    },
    {
      title: 'Generated',
      field: 'generated_at',
      render: (rowData) => rowData.generated_at,
    },
  ];

  const failureSearchData = {
    title: 'No invoices',
    description: 'Please ensure that selected month is correct',
  };

  return (
    <ThemeProvider theme={theme(currentTheme)}>
      <BrowserRouter>
        <Header
          signOut={signOut}
          userName={userName}
          userEmail={userEmail}
          logo={TNSLogo}
          logoMob={TNSLogoMob}
          primaryColor={styles.brandBlueColor500}
          getBrandColors={getBrandColors}
          popupShadow={styles.popupShadow}
          whiteColor={styles.whiteColor}
          darkColor500={styles.darkColor500}
          toggleSidebar={handleToggleSidebar}
          collapseSidebar={handleToggleCollapsed}
          breadcrumbs={breadcrumbsConfig}
        />
        <Routes>
          <Route
            path="/"
            element={(
              <div className="base-layout__content">
                <Sidebar
                  primaryColor={primaryColor}
                  secondaryColor={secondaryColor}
                  getBrandColors={getBrandColors}
                  sidebarConfig={sidebarConfig}
                  toggled={toggled}
                  collapsed={collapsed}
                  toggleSidebar={handleToggleSidebar}
                  collapseSidebar={handleToggleCollapsed}
                  appName="FC Hub"
                  activeLink="/"
                />
                <SimpleTable
                  titles={simpleTableTitles}
                  data={simpleTableData}
                  isLoading={false}
                  primaryColor={primaryColor}
                  failureSearchData={failureSearchData}
                  maxBodyHeight={400}
                />
                <MuiTableProvider>
                  <MuiTable />
                </MuiTableProvider>
              </div>
            )}
          />
        </Routes>
      </BrowserRouter>
    </ThemeProvider>
  );
};

export default App;
