import React from 'react';
import LimitedRowWithShowMore from '../components/LimitedRowWithShowMore';

export default {
  title: 'Components/LimitedRowWithShowMore',
  component: LimitedRowWithShowMore,
};

const Template = (args) => (
  <div style={
  {
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
    marginTop: '60px',
  }
}
  >
    <LimitedRowWithShowMore {...args} />
  </div>
);

const itemsArray = ['DNKTD', 'ESTRB', 'GBRJT', 'KORSK', 'MEXAT', 'MEXIU', 'MEXN3'];
const rowString = itemsArray.join(', ');

const generateTooltipContent = (rowArray) => (
  <div>
    <strong>Custom Tooltip Content</strong>
    <ul>
      {rowArray.map((item) => (
        <li key={item}>{item}</li>
      ))}
    </ul>
  </div>
);

export const Default = Template.bind({});

Default.args = {
  row: rowString,
};

export const WithCustomTooltipComponent = Template.bind({});

WithCustomTooltipComponent.args = {
  row: rowString,
  tooltipComponent: generateTooltipContent(itemsArray),
  limit: 2,
  tooltipWidth: '500px',
};

export const WithoutTooltip = Template.bind({});

WithoutTooltip.args = {
  row: rowString,
  tooltipComponent: generateTooltipContent(itemsArray),
  limit: 2,
  showTooltip: false,
};
