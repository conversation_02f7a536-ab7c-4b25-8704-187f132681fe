import React, { useState } from 'react';

import RemoveEntityModal from 'components/RemoveEntityModal/RemoveEntityModal';

export default {
  title: 'Components/RemoveEntityModal',
  component: RemoveEntityModal,
};

const Template = (args) => {
  const [open, setOpen] = useState(true);
  const onClose = () => setOpen(!open);

  return (
    <RemoveEntityModal
      opened={open}
      handleOpen={onClose}
      removeEntity={onClose}
      {...args}
    />
  );
};

export const Default = Template.bind({});

Default.args = {
  title: 'Remove entity title',
  description: 'Remove entity description',
};
