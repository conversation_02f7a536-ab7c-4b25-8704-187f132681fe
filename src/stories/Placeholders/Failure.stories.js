import React from 'react';
import { GrAlert } from 'react-icons/gr';
import FailurePlaceholder from 'components/FailurePlaceholder';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';

import './FailurePlaceholder.css';

export default {
  title: 'Components/Placeholders/Failure',
  component: FailurePlaceholder,
};

const Template = (args) => (
  <FailurePlaceholder
    {...args}
  />
);

export const Default = Template.bind({});

Default.args = {
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
  errorDescription: 'Your Error description',
  iconSize: 31,
};

export const WithGrAlertIcon = Template.bind({});

WithGrAlertIcon.args = {
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
  errorDescription: 'Using GrAlert icon from react-icons/gr',
  icon: (
    <GrAlert size={31} color={styles.redColor500} />
  ),
};
