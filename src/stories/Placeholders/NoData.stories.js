import React from 'react';
import NoData from 'components/NoData';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import { AiOutlineDollar } from 'react-icons/ai';
import '../assets/styles/NoData.scss';

export default {
  title: 'Components/Placeholders/NoData',
  component: NoData,
};

const Template = (args) => (
  <NoData
    {...args}
  />
);

export const Default = Template.bind({});

Default.args = {
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
  icon: <AiOutlineDollar size={43} />,
  description: 'Your "No data" description',
};

export const WithButton = Template.bind({});

WithButton.args = {
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
  icon: <AiOutlineDollar size={43} />,
  description: 'Your "No data" description',
  additionalItem: (
    <button
      type="button"
      className="table__no-data-btn"
    >
      Click here
    </button>
  ),
};
