import React, { useState } from 'react';
import GTMWrapper from 'components/GTMWrapper';
import useGTM from 'components/GTMWrapper/useGTM';

export default {
  title: 'Components/GTMWrapper',
  component: GTMWrapper,
};
const Template = (args) => {
  const { gtmId } = args;

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <div style={{
        backgroundColor: '#f8f9fa',
        padding: '15px',
        borderRadius: '8px',
        marginBottom: '20px',
        border: '1px solid #dee2e6',
      }}
      >
        <h3 style={{ margin: '0 0 15px 0', color: '#495057' }}>
          GTMWrapper Configuration
        </h3>
        <div>
          <strong>GTM ID:</strong>
          {' '}
          {gtmId || 'Not provided'}
        </div>
        <div style={{ marginTop: '10px' }}>
          <strong>Status:</strong>
          <span style={{
            color: gtmId ? '#28a745' : '#dc3545',
            fontWeight: 'bold',
            marginLeft: '5px',
          }}
          >
            {gtmId ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>

      <div style={{
        backgroundColor: '#d1ecf1',
        padding: '15px',
        borderRadius: '8px',
        border: '1px solid #bee5eb',
      }}
      >
        <p style={{ margin: '0', color: '#0c5460', fontSize: '14px' }}>
          <strong>💡 Note:</strong>
          {' '}
          This component doesn&apos;t render visible content.
          It initializes GTM using the useGTM hook.
        </p>
      </div>

      <GTMWrapper {...args} />
    </div>
  );
};

export const Default = Template.bind({});
Default.args = {
  gtmId: 'GTM-DEMO123',
};

export const WithoutGTMId = Template.bind({});
WithoutGTMId.args = {};

export const WithHookUsage = {
  render: () => {
    const {
      trackEvent, trackPageView, trackClick, isReady,
    } = useGTM('GTM-DEMO-HOOK');
    const [status, setStatus] = useState('');

    const handleTrackEvent = () => {
      try {
        trackEvent('custom_event', {
          event_category: 'storybook',
          event_label: 'button_click',
          value: 1,
        });
        setStatus('✅ Custom event tracked');
      } catch (error) {
        setStatus(`❌ Error: ${error.message}`);
      }
    };

    const handleTrackPageView = () => {
      try {
        trackPageView({
          page: '/storybook/custom-page',
          title: 'Custom Page from Hook',
          section: 'demo',
        });
        setStatus('✅ Page view tracked');
      } catch (error) {
        setStatus(`❌ Error: ${error.message}`);
      }
    };

    const handleTrackClick = () => {
      try {
        trackClick({
          element: 'demo-button',
          location: 'storybook',
        });
        setStatus('✅ Click event tracked');
      } catch (error) {
        setStatus(`❌ Error: ${error.message}`);
      }
    };

    return (
      <div style={{
        padding: '20px',
        fontFamily: 'system-ui',
      }}
      >
        <div style={{
          backgroundColor: '#f8f9fa',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '20px',
          border: '1px solid #dee2e6',
        }}
        >
          <h3 style={{ margin: '0 0 15px 0' }}>useGTM Hook Demo</h3>
          <p style={{
            margin: '0 0 15px 0',
            color: '#6c757d',
          }}
          >
            This demonstrates direct usage of the useGTM hook for manual tracking.
          </p>
          <p style={{ margin: '0 0 15px 0' }}>
            <strong>GTM Ready:</strong>
            <span style={{
              color: isReady() ? '#28a745' : '#dc3545',
              marginLeft: '5px',
            }}
            >
              {isReady() ? 'Yes' : 'No'}
            </span>
          </p>

          <div style={{
            display: 'flex',
            gap: '10px',
            flexWrap: 'wrap',
          }}
          >
            <button
              type="button"
              onClick={handleTrackEvent}
              style={{
                padding: '10px 15px',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Track Custom Event
            </button>

            <button
              type="button"
              onClick={handleTrackPageView}
              style={{
                padding: '10px 15px',
                backgroundColor: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Track Page View
            </button>

            <button
              type="button"
              onClick={handleTrackClick}
              style={{
                padding: '10px 15px',
                backgroundColor: '#ffc107',
                color: 'black',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Track Click
            </button>
          </div>

          {status && (
            <div style={{
              marginTop: '15px',
              padding: '10px',
              backgroundColor: status.includes('✅') ? '#d4edda' : '#f8d7da',
              border: `1px solid ${status.includes('✅') ? '#c3e6cb' : '#f5c6cb'}`,
              borderRadius: '4px',
              color: status.includes('✅') ? '#155724' : '#721c24',
            }}
            >
              {status}
            </div>
          )}
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      storyDescription: 'Demonstrates direct usage of the useGTM hook for manual event tracking. Click buttons to trigger different types of tracking events.',
    },
  },
};
