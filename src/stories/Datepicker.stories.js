import CustomDatePicker from 'components/CustomDatePicker';
import React from 'react';

const defaultArgs = {
  date: new Date(),
  setDate: () => { },
  disabled: false,
  minDate: new Date('2021-01-01'),
  maxDate: new Date('2025-01-01'),
};

const title = 'Components/CustomDatePicker';

export default {
  title,
  component: CustomDatePicker,
};

const Template = (args) => (
  <CustomDatePicker
    {...args}
  />
);

export const Default = Template.bind({});

Default.args = {
  title: 'Custom Datepicker',
  ...defaultArgs,
};
