import React from 'react';
import { Provider } from 'react-redux';
import { createStore } from 'redux';
import { Button } from '@mui/material';
import rootReducer from 'core/storybookConfigs/rootReducer';
import Toastr from 'components/Toastr/Toastr';
import './assets/styles/Toastr.scss';

const getInitialStateForToastr = ({
  type, id, className, title, component,
}) => ({
  toastr: {
    toastrs: [
      {
        id,
        position: 'top-right',
        type,
        options: {
          timeOut: 1000000,
          className,
          closeOnToastrClick: true,
          transitionIn: 'fadeIn',
          transitionOut: 'fadeOut',
          removeOnHover: false,
          showCloseButton: true,
          component,
        },
        title,
      },
    ],
  },
});

const configForInitialStateToastrs = {
  error: {
    type: 'error',
    id: 1,
    className: 'error-toastr',
    title: 'Error title',
  },
  success: {
    type: 'success',
    id: 2,
    className: 'success-toastr',
    title: 'Success title',
  },
  info: {
    type: 'info',
    id: 3,
    className: 'info-toastr',
    title: 'Info title',
  },
  withComponent: {
    type: 'success',
    id: 4,
    className: 'success-toastr',
    title: 'Success title',
    component: (
      <Button variant="outlined" sx={{ ml: 2 }}>
        Open
      </Button>
    ),
  },
};

const store = {
  errorToastr: createStore(
    rootReducer,
    getInitialStateForToastr(configForInitialStateToastrs.error),
  ),
  successToastr: createStore(
    rootReducer,
    getInitialStateForToastr(configForInitialStateToastrs.success),
  ),
  infoToastr: createStore(
    rootReducer,
    getInitialStateForToastr(configForInitialStateToastrs.info),
  ),
  successToastrWithComponent: createStore(
    rootReducer,
    getInitialStateForToastr(configForInitialStateToastrs.withComponent),
  ),
};

const Template = (args) => (
  // eslint-disable-next-line react/destructuring-assignment
  <Provider store={args.store}>
    <Toastr {...args} />
  </Provider>
);

export default {
  title: 'Components/Toastr',
  component: Template,
};

export const Error = Template.bind({});
export const Success = Template.bind({});
export const Info = Template.bind({});
export const WithComponent = Template.bind({});

Error.args = { store: store.errorToastr };
Success.args = { store: store.successToastr };
Info.args = { store: store.infoToastr };
WithComponent.args = { store: store.successToastrWithComponent };
