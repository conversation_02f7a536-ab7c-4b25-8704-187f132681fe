import React from 'react';
import Header<PERSON><PERSON> from 'components/HeaderLogo/HeaderLogo';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import TNSLogo from '../assets/images/ts-nextgen-header-logo.svg';

const router = createBrowserRouter([
  {
    path: '*',
    element: <HeaderLogo logo={TNSLogo} coreUrl="/" />,
  },
]);

const Template = () => <RouterProvider router={router} />;

export default {
  title: 'Components/HeaderLogo',
  component: Template,
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
};

export const Default = Template.bind({});

Default.args = { logo: TNSLogo };
