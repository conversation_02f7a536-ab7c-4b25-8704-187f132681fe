import React from 'react';
import { GrAchievement } from 'react-icons/gr';
import ActionButtonWithTooltip from 'components/ActionButtonWithTooltip/ActionButtonWithTooltip';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

const title = 'Components/ActionButtonWithTooltip';

export default {
  title,
  component: ActionButtonWithTooltip,
};

const Template = (args) => <ActionButtonWithTooltip {...args} />;

export const Default = Template.bind({});

Default.args = {
  title: 'test tooltip',
  icon: <GrAchievement />,
};

export const Background = Template.bind({});

Background.args = {
  title: 'test tooltip',
  icon: <GrAchievement />,
  backgroundColor: '#dfdef4',
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
  primaryColor: '#2D2A81',
};
