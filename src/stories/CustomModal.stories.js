import React, { useState } from 'react';
import CustomModal from 'components/CustomModal';
import { AiOutlineSetting } from 'react-icons/ai';
import './assets/styles/CustomModal.scss';

const defaultArgs = {
  children: <div className="content">Your custom content</div>,
  subTitle: 'Subtitle',
  onClickConfirm: () => {},
  onClickCancel: () => {},
};

const title = 'Components/CustomModal';

export default {
  title,
  component: CustomModal,
};

const Template = (args) => {
  const [open, setOpen] = useState(true);
  return (
    <CustomModal
      handleOpen={() => setOpen(!open)}
      isOpen={open}
      {...args}
    />
  );
};
export const Default = Template.bind({});

Default.args = {
  title: 'Custom Modal',
  ...defaultArgs,
};

export const TitleWithElement = Template.bind({});
TitleWithElement.args = {
  title: (
    <div>
      <AiOutlineSetting />
      <span>Title with Icon</span>
    </div>
  ),
  ...defaultArgs,
};
