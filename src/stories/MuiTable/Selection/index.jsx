import React, { useState } from 'react';
import MuiTable from 'components/MuiTable';
import { MuiTableProvider } from 'components/MuiTable/MuiTableContext';
import { Button } from '@mui/material';
import { mockColumnsSelectionTable, mockRowsSelectionTable } from './mock';

const ActionButton = () => {
  const [counter, setCounter] = useState(0);

  const onClick = () => {
    setCounter(counter + 1);
  };

  return (
    <Button
      sx={{ mr: 3 }}
      variant="contained"
      color="primary"
      type="button"
      onClick={onClick}
    >
      button is clicked
      {' '}
      {counter}
      {' '}
      times
    </Button>
  );
};

const Actions = () => (
  <div style={{ display: 'flex', flexDirection: 'row-reverse' }}>
    <ActionButton />
    <ActionButton />
  </div>
);

const Selection = (args) => {
  const [selectedRowIds, setSelectedRowIds] = useState([]);

  return (
    <MuiTableProvider>
      <MuiTable
        columns={mockColumnsSelectionTable}
        rows={mockRowsSelectionTable}
        rowCount={mockRowsSelectionTable.length}
        rowSelectionModel={selectedRowIds}
        onRowSelectionModelChange={setSelectedRowIds}
        Actions={Actions}
        isVisibleSearchInput
        checkboxSelection
        {...args}
      />
    </MuiTableProvider>

  );
};

Selection.args = {
  primaryColor: 'blue',
};

export default Selection;
