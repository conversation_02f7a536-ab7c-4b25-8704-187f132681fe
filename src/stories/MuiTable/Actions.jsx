import React, { useState } from 'react';
import { Button } from '@mui/material';
import Template from './Template';

const Default = Template.bind({});

const ActionButton = () => {
  const [counter, setCounter] = useState(0);

  const onClick = () => {
    setCounter(counter + 1);
  };

  return (
    <Button
      sx={{ mr: 3 }}
      variant="contained"
      color="primary"
      type="button"
      onClick={onClick}
    >
      button is clicked
      {' '}
      {counter}
      {' '}
      times
    </Button>
  );
};

const Actions = () => (
  <div style={{ display: 'flex', flexDirection: 'row-reverse' }}>
    <ActionButton />
    <ActionButton />
  </div>
);

const ActionsLeft = () => (
  <div style={{ display: 'flex', flexDirection: 'row' }}>
    <ActionButton />
  </div>
);

Default.args = {
  Actions,
  ActionsLeft,
  isPaginationDisabled: true,
};

export default Default;
