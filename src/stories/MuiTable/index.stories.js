import MuiTable from 'components/MuiTable';

export { default as Default } from './Default';
export { default as EmptyCustomComponent } from './EmptyCustomComponent';
export { default as EmptyDefaultComponent } from './EmptyDefaultComponent';
export { default as Selection } from './Selection';
export { default as Wide } from './Wide';
export { default as Sort } from './Sort';
export { default as Pagination } from './Pagination';
export { default as Actions } from './Actions';
export { default as Search } from './Search';
export { default as FixedColumns } from './FixedColumns';
export { default as FixedColumnsRight } from './FixedColumnsRight';
export { default as StickyHeader } from './StickyHeader';
export { default as StickyHeaderWithPagination } from './StickyHeaderWithPagination';
export { default as DisabledVirtualization } from './DisabledVirtualization';

export default {
  title: 'Components/MuiTable',
  component: MuiTable,
};
