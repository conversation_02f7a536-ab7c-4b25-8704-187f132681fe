import React, { useEffect, useState } from 'react';
import MuiTable from 'components/MuiTable';
import { MuiTableProvider } from 'components/MuiTable/MuiTableContext';
import { mockColumnsPaginationTable, mockRowsPaginationTable } from './mock';

const getDataFromTheServer = (page, pageSize) => new Promise((resolve) => {
  const totalCount = mockRowsPaginationTable.length;
  const firstElement = pageSize * page - pageSize;
  const lastElement = firstElement + pageSize > totalCount ? totalCount : firstElement + pageSize;

  const results = mockRowsPaginationTable.slice(firstElement, lastElement);

  const dataForPaging = {
    data: {
      results,
      page,
      pageSize,
      totalCount,
    },
  };

  setTimeout(() => {
    resolve(dataForPaging);
  }, 500);
});

// default params could be got from url or props
const defaultPageSize = 10;
const defaultPage = 1;

const Pagination = (args) => {
  const [isLoading, setIsLoading] = useState(true);
  const [rows, setRows] = useState();
  const [totalCount, setTotalCount] = useState();

  const onChangePaginationHandler = async (pagination) => {
    setIsLoading(true);
    const { data } = await getDataFromTheServer(pagination.page, pagination.pageSize);
    setRows(data.results);
    setTotalCount(data.totalCount);
    setIsLoading(false);
  };

  useEffect(() => {
    onChangePaginationHandler({ page: defaultPage, pageSize: defaultPageSize });
  }, []);

  return (
    <MuiTableProvider
      onChangePagination={onChangePaginationHandler}
      defaultPagination={{ page: defaultPage, pageSize: defaultPageSize }}
    >
      <MuiTable
        rows={rows}
        columns={mockColumnsPaginationTable}
        rowCount={totalCount}
        loading={isLoading}
        showFirstLastPageButtons
        fixedColumns={{
          0: 50,
          1: 130,
        }}
        {...args}
      />
    </MuiTableProvider>
  );
};

Pagination.args = {
  primaryColor: 'blue',
};

export default Pagination;
