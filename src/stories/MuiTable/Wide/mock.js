export const mockColumnsWideTable = [
  {
    headerName: 'ICCID',
    field: 'iccid',
    width: 180,
  },
  {
    headerName: 'MSISDN',
    field: 'msisdn',
    width: 180,
  },
  {
    headerName: 'IMSI',
    field: 'imsi',
    width: 150,
  },
  {
    headerName: 'SIM ID',
    field: 'simId',
    width: 80,
  },
  {
    headerName: 'Rate Plan',
    field: 'subscription.name',
    width: 80,
  },
  {
    headerName: 'IMSI Status',
    field: 'status',
    width: 100,
  },
  {
    headerName: 'SIM Fee Charge',
    field: 'subscription.accessFee',
    width: 100,
  },
  {
    headerName: 'Plan Data Charge',
    field: 'planDataCharge',
    width: 100,
  },
  {
    headerName: 'Roaming Data Charge',
    field: 'roamingDataCharge',
    width: 100,
  },
  {
    headerName: 'Data Charge',
    field: 'dataCharge',
    width: 100,
  },
  {
    headerName: 'Voice Charge',
    field: 'voiceCharge',
    width: 100,
  },
  {
    headerName: 'SMS Charge',
    field: 'smsCharge',
    width: 100,
  },
  {
    headerName: 'Plan Data Volume',
    field: 'planDataVolume',
    width: 100,
  },
  {
    headerName: 'Roaming Data Volume',
    field: 'roamingDataVolume',
    width: 100,
  },
  {
    headerName: 'Data Volume',
    field: 'dataVolume',
    width: 100,
  },
  {
    headerName: 'SMS Volume',
    field: 'smsVolume',
    width: 100,
  },
  {
    headerName: 'SMS MO Volume',
    field: 'smsMOVolume',
    width: 100,
  },
  {
    headerName: 'SMS MT Volume',
    field: 'smsMTVolume',
    width: 100,
  },
  {
    headerName: 'Voice Volume',
    field: 'voiceVolume',
    width: 100,
  },
  {
    headerName: 'Voice MO Volume',
    field: 'voiceMOVolume',
    width: 100,
  },
  {
    headerName: 'Voice MT Volume',
    field: 'voiceMTVolume',
    width: 100,
  },
];

export const mockRowsWideTable = [
  {
    id: 1,
    ratePlanId: 1,
    iccid: '8944538531005853110',
    msisdn: '447452380011',
    imsi: '234588560667871',
    currency: 'GBP',
    status: 'Active',
    serviceUsage: [
      {
        service: 'DATA',
        volume: 653083938292,
        charge: 83.2,
      },
      {
        service: 'VOICE_MO',
        volume: 10,
        charge: 91,
      },
      {
        service: 'VOICE_MT',
        volume: 8,
        charge: 10,
      },
      {
        service: 'SMS_MO',
        volume: 1,
        charge: 11.02,
      },
      {
        service: 'SMS_MT',
        volume: 2,
        charge: 11.9,
      },
    ],
    subscription: {
      charge: 0,
      ratePlanId: 1,
      simsActive: 120,
      simsTotal: 10,
      name: 'some other name',
      accessFee: 1,
    },
  },
  {
    id: 2,
    ratePlanId: 2,
    iccid: '8944538531005853111',
    msisdn: '447452380011',
    imsi: '234588560667871',
    currency: 'GBP',
    status: 'Unknown',
    serviceUsage: [
      {
        service: 'DATA',
        volume: 93083938292,
        charge: 91,
      },
      {
        service: 'VOICE_MO',
        volume: 12,
        charge: 19.1,
      },
      {
        service: 'VOICE_MT',
        volume: 3,
        charge: 12,
      },
      {
        service: 'SMS_MO',
        volume: 61,
        charge: 51.22,
      },
      {
        service: 'SMS_MT',
        volume: 5,
        charge: 14.1,
      },
    ],
    subscription: {
      charge: 0,
      ratePlanId: 1,
      simsActive: 120,
      simsTotal: 10,
      name: 'some name',
      accessFee: 1,
    },
  },
];
