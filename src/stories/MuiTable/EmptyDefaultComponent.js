import React from 'react';
import { AiOutlineLineChart } from 'react-icons/ai';
import Template from './Template';

const EmptyDefaultComponent = Template.bind({});

EmptyDefaultComponent.args = {
  columns: [{
    headerName: 'Id',
    field: 'id',
    width: 40,
  }],
  rows: [],
  rowCount: 0,
  primaryColor: 'blue',
  failureSearchData: {
    icon: <AiOutlineLineChart size={24} color="blue" />,
    title: 'No results found',
    description: 'Try adjusting your search or filter to find what you are looking for',
  },
};

export default EmptyDefaultComponent;
