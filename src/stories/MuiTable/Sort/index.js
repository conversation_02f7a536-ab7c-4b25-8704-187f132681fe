import React, { useEffect, useState } from 'react';
import MuiTable from 'components/MuiTable';
import { MuiTableProvider } from 'components/MuiTable/MuiTableContext';
import { mockColumnsSortTable, mockRowsSortTable } from './mock';

const getDataFromTheServer = ({ field, sort }) => {
  if (!field || !sort) {
    return mockRowsSortTable.slice();
  }

  return mockRowsSortTable.slice().sort((a, b) => {
    const fieldA = a[field].toUpperCase();
    const fieldB = b[field].toUpperCase();

    if (fieldA < fieldB) {
      if (sort === 'asc') {
        return -1;
      }
      return 1;
    }
    if (fieldA > fieldB) {
      if (sort === 'asc') {
        return 1;
      }
      return -1;
    }

    return 0;
  });
};

const Template = (args) => {
  const { defaultSort, ...otherArgs } = args;
  const [rows, setRows] = useState(mockRowsSortTable);

  const onChangeSortHandler = (pagination, { field, sort }) => {
    const sortedRows = getDataFromTheServer({ field, sort });
    setRows(sortedRows);
  };

  useEffect(() => {
    const field = defaultSort?.field;
    const sort = defaultSort?.sort;

    const sortedRows = getDataFromTheServer({ field, sort });
    setRows(sortedRows);
  }, []);

  return (
    <MuiTableProvider
      defaultSort={defaultSort}
      onChangeSort={onChangeSortHandler}
    >
      <MuiTable
        rows={rows}
        {...otherArgs}
      />
    </MuiTableProvider>
  );
};

const Sort = Template.bind({});

Sort.args = {
  columns: mockColumnsSortTable,
  defaultSort: { field: 'name', sort: 'asc' },
  primaryColor: 'blue',
  isPaginationDisabled: true,
};

export default Sort;
