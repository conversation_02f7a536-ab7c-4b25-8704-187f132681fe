export const mockColumnsSortTable = [
  {
    headerName: 'Name',
    field: 'name',
    width: 250,
    sortable: true,
  },
  {
    headerName: 'City',
    field: 'city',
    width: 250,
    sortable: true,
  },
];

export const mockRowsSortTable = [
  {
    id: 1,
    name: 'Larisa',
    city: 'Lanpelan',
  }, {
    id: 2,
    name: 'Delphine',
    city: 'Ciudad Cortés',
  }, {
    id: 3,
    name: 'Christoper',
    city: 'Goworowo',
  }, {
    id: 4,
    name: 'Bernadina',
    city: 'Ronneby',
  }, {
    id: 5,
    name: 'Eliot',
    city: 'Ogden',
  }, {
    id: 6,
    name: 'Juliana',
    city: 'Balakasap',
  }, {
    id: 7,
    name: 'Ferguson',
    city: '<PERSON>oshi',
  }, {
    id: 8,
    name: 'Garfield',
    city: 'Dampol',
  }, {
    id: 9,
    name: 'Ginni',
    city: 'General Luna',
  }, {
    id: 10,
    name: 'Flem',
    city: 'Blangkejeren',
  }, {
    id: 11,
    name: 'Haleigh',
    city: 'Pakxong',
  }, {
    id: 12,
    name: '<PERSON><PERSON>',
    city: 'Dijon',
  }, {
    id: 13,
    name: '<PERSON>',
    city: 'Kildare',
  }, {
    id: 14,
    name: '<PERSON>',
    city: 'Ymittos',
  }, {
    id: 15,
    name: 'Camilla',
    city: 'Belén de los Andaquíes',
  }, {
    id: 16,
    name: 'Rebeka',
    city: 'Liski',
  }, {
    id: 17,
    name: 'Elysee',
    city: 'Budenovetc',
  }, {
    id: 18,
    name: 'Jill',
    city: 'Kirovs’k',
  }, {
    id: 19,
    name: 'Elonore',
    city: 'Torre de Moncorvo',
  }, {
    id: 20,
    name: 'Georgiana',
    city: 'Chilmāri',
  }, {
    id: 21,
    name: 'Florrie',
    city: 'Porto Calvo',
  }, {
    id: 22,
    name: 'Etheline',
    city: 'Lautoka',
  }, {
    id: 23,
    name: 'Andriana',
    city: 'Kebon',
  }, {
    id: 24,
    name: 'Vannie',
    city: 'Chyhyryn',
  }, {
    id: 25,
    name: 'Dollie',
    city: 'Pasaje',
  }, {
    id: 26,
    name: 'Arlana',
    city: 'Wonda',
  }, {
    id: 27,
    name: 'Trudy',
    city: 'Zbraslavice',
  }, {
    id: 28,
    name: 'Margette',
    city: 'Ikalamavony',
  }, {
    id: 29,
    name: 'Chelsy',
    city: 'Rushanzhai',
  }, {
    id: 30,
    name: 'Gerik',
    city: 'Skanör',
  }, {
    id: 31,
    name: 'Joshia',
    city: 'Maigo',
  }, {
    id: 32,
    name: 'Lazare',
    city: 'Kimil’tey',
  }, {
    id: 33,
    name: 'Kelley',
    city: 'Huishi',
  }, {
    id: 34,
    name: 'Omero',
    city: 'Paagahan',
  }, {
    id: 35,
    name: 'Modestine',
    city: 'Rosh Pinna',
  }, {
    id: 36,
    name: 'Maitilde',
    city: 'Chumikan',
  }, {
    id: 37,
    name: 'Colver',
    city: 'Dhankutā',
  }, {
    id: 38,
    name: 'Kristoffer',
    city: 'Eckerö',
  }, {
    id: 39,
    name: 'Garik',
    city: 'São Silvestre',
  }, {
    id: 40,
    name: 'Lynnett',
    city: 'Saint-Priest',
  }, {
    id: 41,
    name: 'Garth',
    city: 'Det Udom',
  }, {
    id: 42,
    name: 'Ellen',
    city: 'Sośno',
  }, {
    id: 43,
    name: 'Chick',
    city: 'Surovikino',
  }, {
    id: 44,
    name: 'Lemmie',
    city: 'Andou',
  }, {
    id: 45,
    name: 'Halette',
    city: 'Zhangzhen',
  }, {
    id: 46,
    name: 'Sky',
    city: 'Kruševac',
  }, {
    id: 47,
    name: 'Stafani',
    city: 'Krasiczyn',
  }, {
    id: 48,
    name: 'Aland',
    city: 'Tashanta',
  }, {
    id: 49,
    name: 'Flora',
    city: 'Sotteville-lès-Rouen',
  }, {
    id: 50,
    name: 'Ransell',
    city: 'Santo Domingo Oeste',
  }, {
    id: 51,
    name: 'Brose',
    city: 'Zhoupu',
  }, {
    id: 52,
    name: 'Dewey',
    city: 'Khlong Toei',
  }, {
    id: 53,
    name: 'Lynette',
    city: 'Bejucal',
  }, {
    id: 54,
    name: 'Jordanna',
    city: 'Baltasar Brum',
  }, {
    id: 55,
    name: 'Leonardo',
    city: 'Hezhi',
  }, {
    id: 56,
    name: 'Luisa',
    city: 'Tampocon',
  }, {
    id: 57,
    name: 'Cammy',
    city: 'Timpas',
  }, {
    id: 58,
    name: 'Damara',
    city: 'Tuplice',
  }, {
    id: 59,
    name: 'Danya',
    city: 'Liji',
  }, {
    id: 60,
    name: 'Aubrey',
    city: 'Huai Yot',
  }, {
    id: 61,
    name: 'Rochell',
    city: 'Daoxian',
  }, {
    id: 62,
    name: 'Beverley',
    city: 'Sabugal',
  }, {
    id: 63,
    name: 'Deck',
    city: 'Stettler',
  }, {
    id: 64,
    name: 'Joyann',
    city: 'Paracuru',
  }, {
    id: 65,
    name: 'Bernadina',
    city: 'Rovnoye',
  }, {
    id: 66,
    name: 'Archibald',
    city: 'Gafsa',
  }, {
    id: 67,
    name: 'Manuel',
    city: 'Castle Bruce',
  }, {
    id: 68,
    name: 'Christoph',
    city: 'Binzhou',
  }, {
    id: 69,
    name: 'Kania',
    city: 'Marseille',
  }, {
    id: 70,
    name: 'Julia',
    city: 'Anao',
  }, {
    id: 71,
    name: 'Elianora',
    city: 'Luobuqiongzi',
  }, {
    id: 72,
    name: 'Josey',
    city: 'Ning’er',
  }, {
    id: 73,
    name: 'Rem',
    city: 'Coutada',
  }, {
    id: 74,
    name: 'Lincoln',
    city: 'Tandel',
  }, {
    id: 75,
    name: 'Philipa',
    city: 'Xiakouyi',
  }, {
    id: 76,
    name: 'Claudina',
    city: 'Neiguan',
  }, {
    id: 77,
    name: 'Gisele',
    city: 'Reina Mercedes',
  }, {
    id: 78,
    name: 'Myca',
    city: 'Dinjo',
  }, {
    id: 79,
    name: 'Kristine',
    city: 'Al Ḩaffah',
  }, {
    id: 80,
    name: 'Lynelle',
    city: 'Kōchi-shi',
  }, {
    id: 81,
    name: 'Andria',
    city: 'Sampacho',
  }, {
    id: 82,
    name: 'Solly',
    city: 'Ruma',
  }, {
    id: 83,
    name: 'Aldrich',
    city: 'Slyudyanka',
  }, {
    id: 84,
    name: 'Cicely',
    city: 'El Mida',
  }, {
    id: 85,
    name: 'Gillan',
    city: 'Stockholm',
  }, {
    id: 86,
    name: 'Mariska',
    city: 'Pindi Bhattiān',
  }, {
    id: 87,
    name: 'Doyle',
    city: 'El Rancho',
  }, {
    id: 88,
    name: 'Valencia',
    city: 'Buta',
  }, {
    id: 89,
    name: 'Tressa',
    city: 'Oropesa',
  }, {
    id: 90,
    name: 'Blondelle',
    city: 'Villa Concepción del Tío',
  }, {
    id: 91,
    name: 'Silvano',
    city: 'Ransiki',
  }, {
    id: 92,
    name: 'Seumas',
    city: 'Kedungbanteng Krajan',
  }, {
    id: 93,
    name: 'Deny',
    city: 'Selaphum',
  }, {
    id: 94,
    name: 'Elijah',
    city: 'Sokol’skoye',
  }, {
    id: 95,
    name: 'Essie',
    city: 'Arrentela',
  }, {
    id: 96,
    name: 'Tobye',
    city: 'Mulan',
  }, {
    id: 97,
    name: 'Jeniece',
    city: 'Haradzishcha',
  }, {
    id: 98,
    name: 'Mikkel',
    city: 'Praimarada',
  }, {
    id: 99,
    name: 'Nataline',
    city: 'Vagney',
  }, {
    id: 100,
    name: 'Brittani',
    city: 'Paris 02',
  },
];
