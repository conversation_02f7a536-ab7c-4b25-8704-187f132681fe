import React from 'react';
import Template from './Template';

const EmptyCustomComponent = Template.bind({});

const styles = {
  height: '100%',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
};

const NoResultsOverlay = () => (
  <div style={styles}>
    <h3>No results found</h3>
  </div>
);

const NoRowsOverlay = () => (
  <div style={styles}>
    <h3>No rows to display</h3>
  </div>
);

EmptyCustomComponent.args = {
  columns: [{
    headerName: 'Id',
    field: 'id',
    width: 40,
  }],
  rows: [],
  rowCount: 0,
  primaryColor: 'blue',
  slots: {
    noRowsOverlay: NoRowsOverlay,
    noResultsOverlay: NoResultsOverlay,
  },
};

export default EmptyCustomComponent;
