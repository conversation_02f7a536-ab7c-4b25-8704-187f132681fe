import React from 'react';
import MuiTable from 'components/MuiTable';
import { MuiTableProvider } from 'components/MuiTable/MuiTableContext';

const Template = (args = {}) => {
  const {
    defaultPagination, onChange,
    onChangePagination, onChangeSearch, initialSearchValue, onChangeSort,
    initialState, defaultSort,
    ...restArgs
  } = args;

  const providerValue = {
    defaultPagination,
    onChange,
    onChangePagination,
    onChangeSearch,
    initialSearchValue,
    onChangeSort,
    initialState,
    defaultSort,
  };

  return (
    <MuiTableProvider {...providerValue}>
      <MuiTable primaryColor="blue" {...restArgs} />
    </MuiTableProvider>
  );
};

export default Template;
