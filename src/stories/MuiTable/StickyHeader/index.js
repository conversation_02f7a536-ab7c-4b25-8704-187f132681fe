import React, { useEffect, useState } from 'react';
import MuiTable from 'components/MuiTable';
import { MuiTableProvider } from 'components/MuiTable/MuiTableContext';
import { mockColumnsSearchTable, mockRowsSearchTable } from './mock';

const getDataFromTheServer = (pagination, sort, searchValue) => new Promise((resolve) => {
  if (!searchValue) {
    const dataForPaging = { data: { results: mockRowsSearchTable } };

    setTimeout(() => {
      resolve(dataForPaging);
    }, 500);

    return;
  }

  const results = [];

  mockRowsSearchTable.forEach((row) => {
    // eslint-disable-next-line no-restricted-syntax
    for (const [, value] of Object.entries(row)) {
      if (value.toString().indexOf(searchValue) !== -1) {
        results.push(row);
        break;
      }
    }
  });

  const dataForPaging = { data: { results } };

  setTimeout(() => {
    resolve(dataForPaging);
  }, 500);
});

const StickyHeader = (args) => {
  const { initialSearchValue, ...otherArgs } = args;

  const [isLoading, setIsLoading] = useState(true);
  const [rows, setRows] = useState();
  const maxTableHeight = '500px';

  const onChangeSearchHandler = async (pagination, sort, searchValue) => {
    setIsLoading(true);
    const { data } = await getDataFromTheServer(pagination, sort, searchValue);
    setRows(data.results);
    setIsLoading(false);
  };

  useEffect(() => {
    onChangeSearchHandler({}, [{}], initialSearchValue);
  }, []);

  return (
    <MuiTableProvider
      onChangeSearch={onChangeSearchHandler}
      initialSearchValue={initialSearchValue}
    >
      <MuiTable
        rows={rows}
        columns={mockColumnsSearchTable}
        loading={isLoading}
        {...otherArgs}
        maxTableHeight={maxTableHeight}
      />
    </MuiTableProvider>
  );
};

StickyHeader.args = {
  isVisibleSearchInput: true,
  isPaginationDisabled: true,
  primaryColor: 'blue',
};

export default StickyHeader;
