import React, { useState } from 'react';
import { MemoryRouter } from 'react-router-dom';
import Sidebar from 'components/Sidebar/Sidebar';
import { AiOutlineUsergroupAdd, AiOutlineSetting, AiOutlineDollar } from 'react-icons/ai';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

import SidebarToggle from '../components/Sidebar/SidebarToggle';

export default {
  title: 'Components/Sidebar',
  component: Sidebar,
};
const getCurrentThemeColors = (color) => getBrandColors(color, 'bt');

const sidebarConfig = [
  {
    id: 'user-management',
    name: 'User management',
    path: '/administration/user-management',
    icon: <AiOutlineUsergroupAdd size={24} className="sidebar__nav-link-icon" />,
  },
  {
    id: 'settings',
    name: 'Settings',
    path: '/administration/settings',
    icon: <AiOutlineSetting size={24} className="sidebar__nav-link-icon" />,
  },
  {
    id: 'budgets',
    name: 'Budgets',
    path: '/administration/budgets',
    icon: <AiOutlineDollar style={{ fontSize: '24px' }} className="sidebar__nav-link-icon" />,
    submenu: [{
      id: 'budgets-list',
      name: 'Budget List',
      path: '/administration/budgets',
    },
    {
      id: 'budgets-user',
      name: 'Budget User',
      path: '/administration/budgets/user',
    },
    ],
  },
];

const Template = (args) => {
  const [toggled, setToggled] = useState(false);
  const [collapsed, setCollapsed] = useState(true);
  const collapseSidebar = () => setCollapsed(!collapsed);
  const toggleSidebar = () => setToggled(!toggled);

  return (
    <MemoryRouter>
      <div>
        <SidebarToggle
          primaryColor="#2D2A81"
          collapseSidebar={collapseSidebar}
          collapsed={collapsed}
          toggleSidebar={toggleSidebar}
        />
        <Sidebar
          toggled={toggled}
          collapsed={collapsed}
          toggleSidebar={toggleSidebar}
          {...args}
        />
      </div>
    </MemoryRouter>
  );
};

export const Default = Template.bind({});

Default.args = {
  sidebarConfig,
  activeLink: 'user-management',
  primaryColor: '#2D2A81',
  secondaryColor: '#F3C73C',
  getCurrentThemeColors,
  appName: 'FCH',
  handleToggleSidebar: () => {},
  handleCloseSidebar: () => {},
};
