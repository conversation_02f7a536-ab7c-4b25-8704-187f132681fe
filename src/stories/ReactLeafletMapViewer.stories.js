import { Box } from '@mui/material';
import ReactLeafletMapViewer from 'components/ReactLeafletMapViewer';
import React from 'react';

export default {
  title: 'components/ReactLeafletMapViewer',
  component: ReactLeafletMapViewer,
};

const Template = (args) => (
  <Box>
    <ReactLeafletMapViewer {...args} />
  </Box>
);

export const Default = Template.bind({});
Default.args = {
  latitude: 23.0225,
  longitude: 72.5714,
  zoom: 12,
  height: '400px',
  width: '100%',
  radius: 1000,
  showMarker: true,
  showCircle: true,
  show: true,
};

export const WithoutCircle = Template.bind({});
WithoutCircle.args = {
  ...Default.args,
  showCircle: false,
};

export const WithoutMarker = Template.bind({});
WithoutMarker.args = {
  ...Default.args,
  showMarker: false,
  circleOptions: {
    color: '#257eca',
    fillColor: '#257eca',
    fillOpacity: 0.5,
    weight: 2,
  },
};

export const ZoomedOut = Template.bind({});
ZoomedOut.args = {
  ...Default.args,
  zoom: 5,
};

export const SmallMap = Template.bind({});
SmallMap.args = {
  ...Default.args,
  height: '250px',
  width: '300px',
};

export const WithCustomSVGMarker = Template.bind({});
WithCustomSVGMarker.args = {
  latitude: 23.0225,
  longitude: 72.5714,
  showMarker: true,
  showCircle: true,
  markerIconUrl: 'https://stage.spogconnected.com/header/static/media/bt-logo.9fa45231a1c049260130ffd21db77fc2.svg', // Your SVG
  iconSize: [40, 40],
  iconAnchor: [20, 40],
  popupAnchor: [0, -30],
  zoom: 16,
  radius: 100,
  circleOptions: {
    color: '#ffffff',
    fillColor: '#ffffff',
    fillOpacity: 0.5,
    weight: 2,
  },
};

export const WithTitle = Template.bind({});
WithTitle.args = {
  ...Default.args,
  data: {
    mcc: '234',
    mnc: '15',
    lac: '259',
    cell: '2651',
    range: 23117,
    source: 'https://opencellid.org/',
  },
};

export const WithOptionalData = Template.bind({});
WithOptionalData.args = {
  ...Default.args,
  data: {
    mcc: '234',
    mnc: '15',
    lac: '259',
    cell: '2651',
    range: 23117,
    source: 'https://opencellid.org/',
  },
  title: 'Cell Tower Location',
};
