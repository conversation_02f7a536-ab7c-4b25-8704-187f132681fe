import {
  Box, But<PERSON>, List,
  ListItemButton, ListItemText,
} from '@mui/material';
import CommonDrawer from 'components/CommonDrawer';
import React, { useState } from 'react';

export default {
  title: 'components/CommonDrawer',
  component: CommonDrawer,
};

const Template = (args) => {
  const [open, setOpen] = useState(args?.open);

  const toggleDrawer = () => setOpen(!open);

  return (
    <>
      <Button onClick={toggleDrawer}>Open Drawer</Button>
      <CommonDrawer {...args} open={open} onClose={toggleDrawer}>
        {args?.children}
      </CommonDrawer>
    </>
  );
};

const boxStyles = {
  display: 'flex',
  justifyContent: 'self-start',
  alignItems: 'center',
  fontSize: '24px',
  p: 2,
};

export const Default = Template.bind({});
Default.args = {
  className: 'drawer',
  anchor: 'left',
  open: false,
  variant: 'temporary',
  children: <Box sx={boxStyles}>Default Content</Box>,
};

export const RightAligned = Template.bind({});
RightAligned.args = {
  ...Default.args,
  className: 'drawer-right',
  anchor: 'right',
  children: <Box sx={boxStyles}>Right Aligned Content</Box>,
};

export const Persistent = Template.bind({});
Persistent.args = {
  ...Default.args,
  className: 'drawer-persistent',
  variant: 'persistent',
  children: <Box sx={boxStyles}>Persistent Content</Box>,
};

export const DrawerWithComplexLayout = Template.bind({});
DrawerWithComplexLayout.args = {
  ...Default.args,
  className: 'drawer-complex-layout',
  children: (
    <Box sx={{ flexDirection: 'column', gap: 2, padding: 2 }}>
      <Box>
        <h4>Section 1</h4>
        <p>Content for the first section.</p>
      </Box>
      <Box>
        <h4>Section 2</h4>
        <p>Content for the second section with more detailed information.</p>
      </Box>
      <Button variant="contained" color="secondary">Action Button</Button>
    </Box>
  ),
};

export const SidebarWithRouteList = Template.bind({});
SidebarWithRouteList.args = {
  ...Default.args,
  className: 'drawer-sidebar',
  children: (
    <Box sx={boxStyles} alignContent="self-start">
      <List>
        {['Home', 'About', 'Services', 'Contact'].map((text) => (
          <ListItemButton key={text}>
            <ListItemText primary={text} />
          </ListItemButton>
        ))}
      </List>
    </Box>
  ),
};
