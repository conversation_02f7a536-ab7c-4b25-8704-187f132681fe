import React from 'react';
import { AiOutlineUnorderedList, AiOutlineUserAdd } from 'react-icons/ai';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import SimpleTable from 'components/SimpleTable';

import { Button, Paper } from '@mui/material';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

const primaryColor = styles.brandBlueColor500;
const secondaryColor = styles.yellowColor500;

const theme = createTheme({
  palette: {
    primary: {
      main: primaryColor,
    },
    secondary: {
      main: secondaryColor,
    },
  },
});

export default {
  title: 'Components/SimpleTable',
  component: SimpleTable,
};

const data = [
  {
    id: 104,
    file_name: 'EID_XML_AFGAR - SWESM.xml',
    file_path: 'eid-files/799b5b4d-9faf-4404-b7bd-2df8e8e25c25',
    loading_date: '2021-09-08 14:09:12',
    processing_status: 'COMPLETED',
  },
  {
    id: 1,
    file_name: 'EID_XML_AFGAR - SWESM.xml',
    file_path: 'eid-files/799b5b4d-9faf-4404-b7bd-2df8e8e25c25',
    loading_date: '2021-08-31 14:28:45',
    processing_status: 'FAILED',
  },
];

const failureSearchData = {
  icon: <AiOutlineUnorderedList size={24} />,
  title: 'No data files',
  description: '',
};

const getStatus = (rowData) => {
  let status = '';

  switch (rowData.processing_status) {
    case 'IN_PROGRESS':
      status = 'In Progress';
      break;
    case 'LOADED':
      status = 'Loaded';
      break;
    case 'COMPLETED':
      status = 'Completed';
      break;
    case 'FAILED':
      status = 'Failed';
      break;
    default:
      status = '';
  }

  return status;
};

const getFilesStatus = (file) => (
  <div className="files__status-wrap">
    {/* eslint-disable-next-line react/destructuring-assignment */}
    <span className={`files__status-badge files__status-${file.processing_status.toLowerCase()}`} />
    {getStatus(file)}
  </div>
);

const ToolBarActionButton = (
  <Button
    sx={{ backgroundColor: primaryColor }}
    variant="contained"
    size="large"
    startIcon={<AiOutlineUserAdd />}
  >
    Add client
  </Button>
);

const titles = [
  {
    title: 'Filename',
    field: 'file_name',
    customFilterAndSearch: (searchValue, rowData) => (
      rowData.file_name.toLowerCase().includes(searchValue.toLowerCase())
    ),
    render: (rowData) => rowData.file_name,
  },
  {
    title: 'Uploaded (UTC)',
    field: 'loading_date',
    customFilterAndSearch: () => {},
    render: (rowData) => rowData.loading_date,
  },
  {
    title: 'Status',
    field: 'processing_status',
    customFilterAndSearch: () => {},
    render: (rowData) => (
      getFilesStatus(rowData)
    ),
  },
];

const updateRow = () => new Promise((resolve) => {
  setTimeout(async () => {
    resolve();
  }, 300);
});

const Template = (args) => (
  <ThemeProvider theme={theme}>
    <Paper sx={{ m: 5, p: 5 }}>
      <SimpleTable {...args} />
    </Paper>
  </ThemeProvider>
);

export const Default = Template.bind({});
export const Editable = Template.bind({});
export const Paging = Template.bind({});

Default.args = {
  data,
  failureSearchData,
  titles,
  searchProperty: 'file_name',
  primaryColor,
  maxBodyHeight: 500,
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
};

Editable.args = {
  data,
  failureSearchData,
  titles,
  searchProperty: 'file_name',
  primaryColor,
  maxBodyHeight: 500,
  selection: true,
  toolBarActionButtons: ToolBarActionButton,
  editable: {
    onRowUpdate: updateRow,
  },
};

Paging.args = {
  data,
  failureSearchData,
  titles,
  searchProperty: 'file_name',
  primaryColor,
  maxBodyHeight: 500,
  selection: true,
  toolBarActionButtons: ToolBarActionButton,
  editable: {
    onRowUpdate: updateRow,
  },
  options: { paging: true },
};
