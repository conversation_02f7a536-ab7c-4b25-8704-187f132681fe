import React from 'react';
import TNSLogo from 'assets/images/ts-nextgen-header-logo.svg';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import Header from 'components/Header/Header';
import { MemoryRouter } from 'react-router-dom';
import 'components/Header/HeaderBreadcrumbs/HeaderBreadcrumbs.scss';
import TNSLogoMob from '../assets/images/ts-nextgen-header-logo-mob.svg';

export default {
  title: 'Components/Header',
  component: Header,
};

const headerBreadcrumbs = [{ path: '*', breadcrumb: 'Header' }];
const getCurrentThemeColors = (color) => getBrandColors(color, 'bt');

const headerMenuData = [
  {
    id: 1,
    title: 'Application Management',
    description: 'Add new Applications or manage already created Applications',
    url: '',
    icon: 'https://www.svgrepo.com/show/532046/cloud-up-arrow.svg',
  },
  {
    id: 4,
    title: 'Organization Management',
    description: 'Add new Organizations or edit already created Organizations',
    url: '',
    icon: 'https://www.svgrepo.com/show/532314/id-badge.svg',
  },
  {
    id: 5,
    title: 'Administration',
    description: 'Distributor Administration',
    url: '',
    icon: 'https://www.svgrepo.com/show/532244/gear.svg',
  },
  {
    id: 6,
    title: 'Notification',
    description: 'Notfication description',
    url: '',
    icon: 'https://www.svgrepo.com/show/532089/bell-alt.svg',
  },
];

const Template = (args) => (
  <MemoryRouter>
    <Header {...args} primaryColor="#2D2A81" />
  </MemoryRouter>
);
export const LoggedIn = Template.bind({});

LoggedIn.args = {
  signOut: () => {},
  userName: 'Test',
  userEmail: '<EMAIL>',
  logo: TNSLogo,
  logoMob: TNSLogoMob,
  getCurrentThemeColors,
  popupShadow: '0px 6px 16px rgba(8, 35, 48, 0.15)',
  whiteColor: '#ffffff',
  darkColor500: '#1C1C28',
  coreUrl: '/',
  breadcrumbs: headerBreadcrumbs,
  headerMenuData,
  hubspotMeetingUrl: 'https://meetings-eu1.hubspot.com/serhii-honcharov?embed=true',
  administrationData: {
    description: 'Distributor Administration',
    icon: 'https://alegflgbdr.cloudimg.io/__applications-dev__/c8d21a2f-832e-4e84-b18c-cb48d27b8445',
    id: 5,
    title: 'Administration',
    url: '',
  },
  notificationData: {
    description: 'Notfication description',
    icon: 'https://alegflgbdr.cloudimg.io/__applications-dev__/8e2ee17b-be52-41a6-9ea2-c65ef4aa9a6a',
    id: 6,
    title: 'Notification',
    url: '',
  },
  notificationCount: 3,
  isLoadingUserInfo: false,
  isLoadingApplicationsManagementData: false,
  isLoadingShortApplications: false,
  isSidebar: false,
};
