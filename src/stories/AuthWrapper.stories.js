import React from 'react';
import permissions from 'components/AuthWrapper/permission';
import AuthWrapper from 'components/AuthWrapper';

const defaultArgs = {
  permission: ['Create Account'],
  repository: ['AccountManagement', 'MarketShareReport'],
  isComponent: true,
  access: permissions,
};

const title = 'Components/AuthWrapper';
export default {
  title,
  component: AuthWrapper,
};
const Template = (args) => (
  <AuthWrapper {...args}>
    <button type="button">Create Account</button>
  </AuthWrapper>
);

export const Default = Template.bind({});

Default.args = {
  title: 'Custom AuthWrapper',
  ...defaultArgs,
};
