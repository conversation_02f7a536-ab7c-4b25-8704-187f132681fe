import React from 'react';
import { MemoryRouter } from 'react-router-dom';
import Forbidden from 'components/Forbidden';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

export default {
  title: 'Components/Forbidden',
  component: Forbidden,
};

const Template = (args) => (
  <MemoryRouter>
    <Forbidden
      {...args}
    />
  </MemoryRouter>
);

export const Default = Template.bind({});

Default.args = {
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
  primaryColor: '#F200F5',
  secondaryColor: '#5514B4',
};
