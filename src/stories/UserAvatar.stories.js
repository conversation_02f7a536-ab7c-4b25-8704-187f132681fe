import React from 'react';

import UserAvatar from 'components/UserAvatar/UserAvatar';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

export default {
  title: 'Components/UserAvatar',
  component: UserAvatar,
};

const Template = (args) => <UserAvatar {...args} />;

export const Default = Template.bind({});

Default.args = {
  userName: 'Test user',
  getBrandColors,
  primaryColor: '#2D2A81',
};
