import React from 'react';
import BackButton from 'components/BackButton';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';

const router = createBrowserRouter([
  {
    path: '*',
    element: <BackButton path="/" />,
  },
]);

const Template = () => <RouterProvider router={router} />;

export default {
  title: 'Components/BackButton',
  component: Template,
};

export const Default = Template.bind({});

Default.args = {
  path: '/',
};
