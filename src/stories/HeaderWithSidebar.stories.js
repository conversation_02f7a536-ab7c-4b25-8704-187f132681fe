import React, { useState } from 'react';
import TNSLogo from 'assets/images/ts-nextgen-header-logo.svg';
import TNSLogoMob from 'assets/images/ts-nextgen-header-logo-mob.svg';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import Header from 'components/Header/Header';
import { MemoryRouter } from 'react-router-dom';

import 'components/Header/HeaderBreadcrumbs/HeaderBreadcrumbs.scss';
import { AiOutlineDollar, AiOutlineSetting, AiOutlineUsergroupAdd } from 'react-icons/ai';

import Sidebar from '../components/Sidebar';

export default {
  title: 'Components/Header',
  component: Header,
};

const headerBreadcrumbs = [{ path: '*', breadcrumb: 'Header' }];

const headerMenuData = [
  {
    id: 1,
    title: 'Application Management',
    description: 'Add new Applications or manage already created Applications',
    url: '',
    icon: 'https://www.svgrepo.com/show/532046/cloud-up-arrow.svg',
  },
  {
    id: 4,
    title: 'Organization Management',
    description: 'Add new Organizations or edit already created Organizations',
    url: '',
    icon: 'https://www.svgrepo.com/show/532314/id-badge.svg',
  },
  {
    id: 5,
    title: 'Administration',
    description: 'Distributor Administration',
    url: '',
    icon: 'https://www.svgrepo.com/show/532244/gear.svg',
  },
];

const sidebarConfig = [
  {
    id: 'user-management',
    name: 'User management',
    path: '/administration/user-management',
    icon: <AiOutlineUsergroupAdd size={24} className="sidebar__nav-link-icon" />,
  },
  {
    id: 'settings',
    name: 'Settings',
    path: '/administration/settings',
    icon: <AiOutlineSetting size={24} className="sidebar__nav-link-icon" />,
  },
  {
    id: 'budgets',
    name: 'Budgets',
    path: '/administration/budgets',
    icon: <AiOutlineDollar style={{ fontSize: '24px' }} className="sidebar__nav-link-icon" />,
    submenu: [{
      id: 'budgets-list',
      name: 'Budget List',
      path: '/administration/budgets',
    },
    {
      id: 'budgets-user',
      name: 'Budget User',
      path: '/administration/budgets/user',
    },
    ],
  },
];
const sidebarProps = {
  sidebarConfig,
  activeLink: 'user-management',
  primaryColor: '#2D2A81',
  secondaryColor: '#F3C73C',
  appName: 'FCH',
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
};
const Template = (args) => {
  const [toggled, setToggled] = useState(false);
  const [collapsed, setCollapsed] = useState(true);
  const collapseSidebar = () => setCollapsed(!collapsed);
  const toggleSidebar = () => setToggled(!toggled);
  return (
    <MemoryRouter>
      <Header
        {...args}
        primaryColor="#2D2A81"
        collapseSidebar={collapseSidebar}
        toggleSidebar={toggleSidebar}
        collapsed={collapsed}
      />
      <div style={{ marginTop: '60px' }}>
        <Sidebar
          {...sidebarProps}
          collapseSidebar={collapseSidebar}
          toggleSidebar={toggleSidebar}
          collapsed={collapsed}
          toggled={toggled}
        />
      </div>
    </MemoryRouter>
  );
};
export const HeaderWithSidebar = Template.bind({});

HeaderWithSidebar.args = {
  signOut: () => {},
  userName: 'Test',
  userEmail: '<EMAIL>',
  logo: TNSLogo,
  logoMob: TNSLogoMob,
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
  popupShadow: '0px 6px 16px rgba(8, 35, 48, 0.15)',
  whiteColor: '#ffffff',
  darkColor500: '#1C1C28',
  coreUrl: process.env.REACT_APP_LANDING_URL,
  breadcrumbs: headerBreadcrumbs,
  headerMenuData,
  hubspotMeetingUrl: 'https://meetings-eu1.hubspot.com/serhii-honcharov?embed=true',
  administrationData: {
    description: 'Distributor Administration',
    icon: 'https://alegflgbdr.cloudimg.io/__applications-dev__/c8d21a2f-832e-4e84-b18c-cb48d27b8445',
    id: 5,
    title: 'Administration',
    url: '',
  },
  isLoadingUserInfo: false,
  isLoadingApplicationsManagementData: false,
  isLoadingShortApplications: false,
};
