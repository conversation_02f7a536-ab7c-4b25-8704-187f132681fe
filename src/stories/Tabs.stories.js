import React, { useState } from 'react';

import Tabs from 'components/Tabs';
import TabPanel from 'components/TabPanel';

import {
  AiOutlineSetting, AiOutlineUsergroupAdd, AiFillEyeInvisible, AiOutlineDollar,
} from 'react-icons/ai';
import { Chip } from '@mui/material';

const defaultTabItemsConfig = [
  {
    name: 'Receive',
  },
  {
    name: 'Pay',
  },

];

const iconTabItemsConfig = [
  {
    name: 'Clients',
    icon: <AiOutlineUsergroupAdd size={21} />,
  },
  {
    name: 'Settings',
    icon: <AiOutlineSetting size={21} />,

  },
  {
    name: 'Disabled',
    icon: <AiFillEyeInvisible size={21} />,
    disabled: true,

  },
  {
    name: 'With additional component',
    icon: <AiOutlineDollar size={21} />,
    dataComponent: <Chip label="0" />,
  },
];
const defaultTabValue = 0;

const defaultArgs = {
  tabItemsConfig: defaultTabItemsConfig,
  primaryColor: '#2D2A81',
};

const iconsArgs = {
  tabItemsConfig: iconTabItemsConfig,
  primaryColor: '#2D2A81',
};

const title = 'Components/Tabs';

export default {
  title,
  component: Tabs,
};

const Template = (args) => {
  const [tabIndex, setTabIndex] = useState(defaultTabValue);
  const selectedNewTab = async (event, newTabValue) => {
    setTabIndex(newTabValue);
  };
  const { tabItemsConfig } = args;
  return (
    <div>
      <Tabs
        tabIndex={tabIndex}
        setTabIndex={setTabIndex}
        selectedNewTab={selectedNewTab}
        selectedTab={tabIndex}
        {...args}
      />
      {tabItemsConfig.map((item, index) => (
        <TabPanel
          key={item.name.toString()}
          value={tabIndex}
          index={index}
          className="budget-items__tab-panel"
        >
          {item.children || item.name}
        </TabPanel>
      ))}
    </div>
  );
};

export const Simple = Template.bind({});
export const IconsTab = Template.bind({});

Simple.args = {
  title: 'Two tabs with TabPanel',
  ...defaultArgs,
};

IconsTab.args = {
  title: 'Two Icons Tabs with TabPanel',
  ...iconsArgs,
};
