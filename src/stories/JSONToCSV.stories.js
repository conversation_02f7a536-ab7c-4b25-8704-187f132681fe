import React from 'react';

import JSONToCSV from 'components/JSONToCSV/JSONToCSV';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import { GridExport } from 'assets/images/ImagePlaceholder';

const title = 'Components/JSONToCSV';

const getData = () => (
  [
    { name: '<PERSON>', age: 30, email: '<EMAIL>' },
    { name: '<PERSON>', age: 25, email: '<EMAIL>' },
    { name: '<PERSON>', age: 30, email: '<EMAIL>' },
    { name: '<PERSON>', age: 25, email: '<EMAIL>' },
  ]
);

export default {
  title,
  component: JSONToCSV,
  delimiter: ',',
  fileName: 'Test',
  getData: () => getData(),
  disabled: false,
};

const Template = (args) => (
  <JSONToCSV
    {...args}
  />
);

export const Default = Template.bind({});

Default.args = {
  title: 'Tooltip',
  icon: <GridExport />,
  delimiter: ',',
  fileName: 'Default',
  getData: () => getData(),
};

export const Background = Template.bind({});

Background.args = {
  title: 'Tooltip',
  icon: <GridExport />,
  delimiter: ',',
  fileName: 'BackGround',
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
  primaryColor: '#2D2A81',
  getData: () => getData(),
};

export const Disabled = Template.bind({});

Disabled.args = {
  title: 'Tooltip',
  icon: <GridExport />,
  delimiter: ',',
  fileName: 'BackGround',
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
  primaryColor: '#2D2A81',
  getData: () => getData(),
  disabled: true,
};

export const Delimiter = Template.bind({});

Delimiter.args = {
  title: 'Tooltip',
  icon: <GridExport />,
  delimiter: '|',
  fileName: 'BackGround',
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
  primaryColor: '#2D2A81',
  getData: () => getData(),
  disabled: true,
};
