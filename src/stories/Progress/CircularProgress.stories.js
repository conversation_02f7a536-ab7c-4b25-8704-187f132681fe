import React from 'react';

import CustomCircularProgress from 'components/CustomCircularProgress';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

export default {
  title: 'Components/Progress/CircularProgress',
  component: CustomCircularProgress,
};

const Template = (args) => (
  <CustomCircularProgress
    {...args}
  />
);

export const Default = Template.bind({});

Default.args = {
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
  primaryColor: '#2D2A81',
};
