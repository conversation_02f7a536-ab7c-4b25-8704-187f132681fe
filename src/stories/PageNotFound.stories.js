import React from 'react';
import { MemoryRouter } from 'react-router-dom';
import PageNotFound from 'components/PageNotFound';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

export default {
  title: 'Components/PageNotFound',
  component: PageNotFound,
};

const Template = (args) => (
  <MemoryRouter>
    <PageNotFound
      {...args}
    />
  </MemoryRouter>
);

export const Default = Template.bind({});

Default.args = {
  getCurrentThemeColors: (color) => getBrandColors(color, 'bt'),
  primaryColor: '#F200F5',
  secondaryColor: '#5514B4',
};
