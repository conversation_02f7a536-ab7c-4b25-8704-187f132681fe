import React from 'react';
import { AiOutlineShopping } from 'react-icons/ai';

const breadcrumbs = {
  applicationStore: 'Application store',
  userManagement: 'Administration /',
  settings: 'Administration /',
  home: '',
};

const additionalBreadcrumbs = {
  userManagement: ' User management',
  settings: ' Settings',
};

const paths = {
  applicationStore: '/application-store/',
  userManagement: '/administration/user-management',
  settings: '/administration/settings',
  home: '/',
};

export default [
  {
    path: paths.applicationStore,
    breadcrumb: breadcrumbs.applicationStore,
    icon: <AiOutlineShopping size={24} className="header-breadcrumbs__icon" />,
  },
  {
    path: paths.userManagement,
    breadcrumb: breadcrumbs.userManagement,
    additionalBreadcrumb: additionalBreadcrumbs.userManagement,
  },
  {
    path: paths.settings,
    breadcrumb: breadcrumbs.settings,
    additionalBreadcrumb: additionalBreadcrumbs.settings,
  },
  {
    path: paths.home,
    breadcrumb: breadcrumbs.home,
  },
];
