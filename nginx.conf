server {
    listen 80;
    sendfile on;
    default_type application/octet-stream;
    gzip on;
    gzip_http_version 1.1;
    gzip_disable      "MSIE [1-6]\.";
    gzip_min_length   1100;
    gzip_vary         on;
    gzip_proxied      expired no-cache no-store private auth;
    gzip_types        text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_comp_level   9;
    server_name _;
    root /usr/share/nginx/html;

    location /storybook {
        root /app;
    }
}
